"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    var _getCurrentSession, _getCurrentSession1, _getCurrentSession2, _getCurrentSession3;\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newSessionName, setNewSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            setCurrentSessionId(sessionManager.getCurrentSessionId());\n        }\n    }[\"SessionManager.useEffect\"], []);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleCreateNewSession = async ()=>{\n        if (!newSessionName.trim()) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter a session name\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const sessionId = await sessionManager.createNewSession(newSessionName.trim(), config);\n            sessionManager.setCurrentSession(sessionId);\n            setCurrentSessionId(sessionId);\n            setNewSessionName('');\n            loadSessions();\n            toast({\n                title: \"Session Created\",\n                description: 'New session \"'.concat(newSessionName, '\" has been created')\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to create session. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveCurrentSession = ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const success = sessionManager.saveSession(currentSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus === 'Running');\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Session Saved\",\n                description: \"Current session has been saved successfully\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime) return '0m';\n        const minutes = Math.floor(runtime / 60000);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes % 60, \"m\");\n        }\n        return \"\".concat(minutes, \"m\");\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Session\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: getCurrentSession() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium\",\n                                                    children: (_getCurrentSession = getCurrentSession()) === null || _getCurrentSession === void 0 ? void 0 : _getCurrentSession.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (_getCurrentSession1 = getCurrentSession()) === null || _getCurrentSession1 === void 0 ? void 0 : _getCurrentSession1.pair\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: botSystemStatus === 'Running' ? 'default' : 'secondary',\n                                            children: botSystemStatus === 'Running' ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Runtime:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 font-medium\",\n                                                    children: formatRuntime((_getCurrentSession2 = getCurrentSession()) === null || _getCurrentSession2 === void 0 ? void 0 : _getCurrentSession2.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Trades:\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 font-medium\",\n                                                    children: ((_getCurrentSession3 = getCurrentSession()) === null || _getCurrentSession3 === void 0 ? void 0 : _getCurrentSession3.totalTrades) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSaveCurrentSession,\n                                    className: \"w-full btn-neo\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Save Session\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Create a new session to get started\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    sessions.filter((s)=>s.id !== currentSessionId).length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    sessions.filter((s)=>s.id !== currentSessionId).length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: sessions.filter((s)=>s.id !== currentSessionId).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: sessions.filter((s)=>s.id !== currentSessionId).map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        value: editingName,\n                                                                        onChange: (e)=>setEditingName(e.target.value),\n                                                                        onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                        className: \"text-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleRenameSession(session.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: session.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>{\n                                                                            setEditingSessionId(session.id);\n                                                                            setEditingName(session.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-2 text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            \"Pair: \",\n                                                                            session.pair\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            \"Trades: \",\n                                                                            session.totalTrades\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            \"Runtime: \",\n                                                                            formatRuntime(session.runtime)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            \"P/L: \",\n                                                                            session.totalProfitLoss.toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(session.lastModified), 'MMM dd, yyyy HH:mm')\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1 ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, session.id, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"lp/QLKcSffgIr/7AyXZEoUbg5Q0=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});