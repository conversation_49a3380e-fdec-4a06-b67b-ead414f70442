"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useTradingContext } from '@/contexts/TradingContext';
import { SessionManager } from '@/lib/session-manager';
import type { SessionMetadata, OrderHistoryEntry, TradingConfig } from '@/lib/types';
import { TrendingUp, TrendingDown, Percent, BarChart3, Clock, Target } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format } from 'date-fns';

// Helper function to calculate KPIs from order history
const calculateKPIs = (orderHistory: OrderHistoryEntry[], config: TradingConfig) => {
  const sellTrades = orderHistory.filter(trade => trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined);
  const totalProfitLossCrypto2 = sellTrades.reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto2 || 0), 0);
  const totalProfitLossCrypto1 = sellTrades.reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto1 || 0), 0);
  const profitableTrades = sellTrades.filter(trade => (trade.realizedProfitLossCrypto2 || 0) > 0).length;
  const winRate = sellTrades.length > 0 ? (profitableTrades / sellTrades.length) * 100 : 0;
  const totalTradesExecuted = orderHistory.length;
  const buyTrades = orderHistory.filter(trade => trade.orderType === 'BUY').length;
  const avgProfitPerTradeCrypto2 = sellTrades.length > 0 ? totalProfitLossCrypto2 / sellTrades.length : 0;
  const avgProfitPerTradeCrypto1 = sellTrades.length > 0 ? totalProfitLossCrypto1 / sellTrades.length : 0;

  return {
    totalProfitLossCrypto1: parseFloat(totalProfitLossCrypto1.toFixed(config.numDigits)),
    totalProfitLossCrypto2: parseFloat(totalProfitLossCrypto2.toFixed(config.numDigits)),
    winRate: parseFloat(winRate.toFixed(2)),
    totalTradesExecuted,
    buyTrades,
    sellTrades: sellTrades.length,
    avgProfitPerTradeCrypto2: parseFloat(avgProfitPerTradeCrypto2.toFixed(config.numDigits)),
    avgProfitPerTradeCrypto1: parseFloat(avgProfitPerTradeCrypto1.toFixed(config.numDigits)),
  };
};

// Generate P&L chart data
const generatePnlChartData = (orderHistory: OrderHistoryEntry[], crypto2Symbol: string) => {
  const sellTrades = orderHistory.filter(trade => trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined);
  let cumulativePnL = 0;
  
  return sellTrades.map((trade, index) => {
    cumulativePnL += trade.realizedProfitLossCrypto2 || 0;
    return {
      date: format(new Date(trade.timestamp), 'MMM dd HH:mm'),
      pnl: parseFloat(cumulativePnL.toFixed(4)),
      trade: index + 1,
    };
  });
};

export default function SessionAwareAnalytics() {
  const { orderHistory, config, getDisplayOrders } = useTradingContext();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState<string>('current');
  const [selectedSessionHistory, setSelectedSessionHistory] = useState<OrderHistoryEntry[]>([]);
  const [selectedSessionConfig, setSelectedSessionConfig] = useState<TradingConfig>(config);
  const sessionManager = SessionManager.getInstance();

  useEffect(() => {
    loadSessions();
  }, []);

  useEffect(() => {
    if (selectedSessionId === 'current') {
      setSelectedSessionHistory(orderHistory);
      setSelectedSessionConfig(config);
    } else {
      const session = sessionManager.loadSession(selectedSessionId);
      if (session) {
        setSelectedSessionHistory(session.orderHistory);
        setSelectedSessionConfig(session.config);
      }
    }
  }, [selectedSessionId, orderHistory, config]);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };

  const kpis = useMemo(() => calculateKPIs(selectedSessionHistory, selectedSessionConfig), [selectedSessionHistory, selectedSessionConfig]);

  const pnlChartData = useMemo(
    () => generatePnlChartData(selectedSessionHistory, selectedSessionConfig.crypto2),
    [selectedSessionHistory, selectedSessionConfig.crypto2]
  );

  const currentUnrealizedPL = useMemo(() => {
    if (selectedSessionId !== 'current') return '0.0000'; // Only show unrealized P/L for current session
    
    const currentActiveOrders = getDisplayOrders();
    const rawValue = currentActiveOrders.reduce((sum, order) => {
      if (order.status === 'Full' && order.incomeCrypto2 !== undefined) {
        return sum + order.incomeCrypto2;
      }
      return sum;
    }, 0);
    return rawValue.toFixed(selectedSessionConfig.numDigits);
  }, [getDisplayOrders, selectedSessionConfig.numDigits, selectedSessionId]);

  const getSelectedSessionInfo = () => {
    if (selectedSessionId === 'current') {
      const displayPair = (config.crypto1 && config.crypto2)
        ? `${config.crypto1}/${config.crypto2}`
        : "Crypto 1/Crypto 2";
      return {
        name: 'Current Session',
        pair: displayPair,
        isActive: true
      };
    }
    return sessions.find(s => s.id === selectedSessionId);
  };

  const selectedSession = getSelectedSessionInfo();

  const kpiCards = useMemo(() => [
    {
      title: `Total Realized P/L (${selectedSessionConfig.crypto1 || "Crypto 1"})`,
      value: kpis.totalProfitLossCrypto1,
      icon: <TrendingUp className="h-6 w-6 text-primary" />,
      description: "Sum of profits from sell trades in Crypto1",
      isProfit: kpis.totalProfitLossCrypto1 >= 0
    },
    {
      title: `Total Realized P/L (${selectedSessionConfig.crypto2 || "Crypto 2"})`,
      value: kpis.totalProfitLossCrypto2,
      icon: <TrendingUp className="h-6 w-6 text-primary" />,
      description: "Sum of profits from sell trades in Crypto2",
      isProfit: kpis.totalProfitLossCrypto2 >= 0
    },
    {
      title: "Win Rate",
      value: `${kpis.winRate}%`,
      icon: <Percent className="h-6 w-6 text-primary" />,
      description: "Profitable sell trades / Total sell trades",
      isProfit: kpis.winRate >= 50
    },
    {
      title: "Total Trades",
      value: kpis.totalTradesExecuted,
      icon: <BarChart3 className="h-6 w-6 text-primary" />,
      description: `${kpis.buyTrades} buys, ${kpis.sellTrades} sells`,
      isProfit: true
    },
    {
      title: `Avg Profit/Trade (${selectedSessionConfig.crypto2 || "Crypto 2"})`,
      value: kpis.avgProfitPerTradeCrypto2,
      icon: <Target className="h-6 w-6 text-primary" />,
      description: "Average profit per sell trade",
      isProfit: kpis.avgProfitPerTradeCrypto2 >= 0
    },
    {
      title: `Current Unrealized P/L (${selectedSessionConfig.crypto2 || "Crypto 2"})`,
      value: currentUnrealizedPL,
      icon: <Clock className="h-6 w-6 text-primary" />,
      description: "Unrealized profit/loss from active positions",
      isProfit: parseFloat(currentUnrealizedPL) >= 0,
      isCurrentOnly: true
    },
  ], [kpis, selectedSessionConfig, currentUnrealizedPL]);

  return (
    <div className="space-y-6">
      {/* Session Selection */}
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-primary">Session Analytics</CardTitle>
          <CardDescription>View trading analytics for current and past sessions.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Select Session:</label>
              <Select value={selectedSessionId} onValueChange={setSelectedSessionId}>
                <SelectTrigger className="w-full sm:w-[300px]">
                  <SelectValue placeholder="Select a session" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current">
                    <div className="flex items-center gap-2">
                      <Badge variant="default" className="text-xs">Current</Badge>
                      <span>Current Session ({config.crypto1}/{config.crypto2})</span>
                    </div>
                  </SelectItem>
                  {sessions.length > 0 && (
                    <>
                      <Separator className="my-1" />
                      {sessions.map((session) => (
                        <SelectItem key={session.id} value={session.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">Past</Badge>
                            <span>{session.name} ({session.pair})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Session Info */}
          {selectedSession && (
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center gap-4">
                <div>
                  <h3 className="font-medium">{selectedSession.name}</h3>
                  <p className="text-sm text-muted-foreground">{selectedSession.pair}</p>
                </div>
                {selectedSession.isActive && (
                  <Badge variant="default" className="text-xs">Active</Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {kpiCards.map((kpi, index) => {
          // Hide current-only KPIs for past sessions
          if (kpi.isCurrentOnly && selectedSessionId !== 'current') {
            return null;
          }
          
          return (
            <Card key={index} className="border-2 border-border">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{kpi.title}</CardTitle>
                {kpi.icon}
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${
                  typeof kpi.value === 'number' 
                    ? kpi.isProfit 
                      ? 'text-green-600' 
                      : 'text-red-600'
                    : 'text-foreground'
                }`}>
                  {typeof kpi.value === 'number' ? kpi.value.toFixed(selectedSessionConfig.numDigits) : kpi.value}
                </div>
                <p className="text-xs text-muted-foreground">{kpi.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* P&L Chart */}
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-primary">
            Cumulative Profit/Loss Over Time ({selectedSessionConfig.crypto2})
          </CardTitle>
          <CardDescription>Chart visualization of trading performance for {selectedSession?.name || 'selected session'}.</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          {pnlChartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={pnlChartData} margin={{ top: 5, right: 20, left: -25, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis
                  dataKey="date"
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `${value.toFixed(2)}`}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-card border border-border rounded-lg p-3 shadow-lg">
                          <p className="text-sm font-medium">{`Date: ${label}`}</p>
                          <p className="text-sm">
                            <span className="text-muted-foreground">P/L: </span>
                            <span className={payload[0].value >= 0 ? 'text-green-600' : 'text-red-600'}>
                              {payload[0].value} {selectedSessionConfig.crypto2 || "Crypto 2"}
                            </span>
                          </p>
                          <p className="text-xs text-muted-foreground">Trade #{payload[0].payload.trade}</p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="pnl"
                  stroke="hsl(var(--primary))"
                  strokeWidth={2}
                  dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No sell trades recorded yet for this session.</p>
                <p className="text-xs">Chart will appear after first profitable trade.</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
