"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config) {\n        const autoGeneratedName = this.generateSessionName(config);\n        return this.createNewSession(autoGeneratedName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: session.runtime + (Date.now() - session.lastModified)\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});