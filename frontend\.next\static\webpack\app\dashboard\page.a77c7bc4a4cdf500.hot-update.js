"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        window.addEventListener('online', this.handleOnline.bind(this));\n        window.addEventListener('offline', this.handleOffline.bind(this));\n        // Listen for visibility change to check connection when tab becomes active\n        document.addEventListener('visibilitychange', ()=>{\n            if (!document.hidden) {\n                this.checkConnection();\n            }\n        });\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true, !this.hasInitialized);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false, !this.hasInitialized);\n    }\n    async checkConnection() {\n        try {\n            // Try to fetch a small resource to verify actual connectivity\n            const response = await fetch('/api/health', {\n                method: 'HEAD',\n                cache: 'no-cache',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            const isConnected = response.ok;\n            if (isConnected !== this.isOnline) {\n                this.isOnline = isConnected;\n                this.notifyListeners(isConnected, !this.hasInitialized);\n                if (isConnected) {\n                    this.lastOnlineTime = Date.now();\n                    this.reconnectAttempts = 0;\n                }\n            }\n            return isConnected;\n        } catch (error) {\n            // If fetch fails, we're likely offline\n            if (this.isOnline) {\n                this.isOnline = false;\n                this.notifyListeners(false, !this.hasInitialized);\n            }\n            return false;\n        }\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 30000); // Check every 30 seconds\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(\"\\uD83C\\uDF10 Network: Attempting reconnect \".concat(this.reconnectAttempts, \"/\").concat(this.maxReconnectAttempts, \" in \").concat(delay, \"ms\"));\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n    constructor(){\n        this.isOnline = navigator.onLine;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.hasInitialized = false;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n        // Mark as initialized after a short delay to avoid initial notifications\n        setTimeout(()=>{\n            this.hasInitialized = true;\n        }, 1000);\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        window.addEventListener('beforeunload', ()=>{\n            if (this.saveFunction) {\n                console.log('💾 Auto-save: Saving before page unload');\n                this.saveFunction();\n            }\n        });\n        // Also save on page visibility change (when user switches tabs)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.saveFunction) {\n                console.log('💾 Auto-save: Saving on tab switch');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    enable(saveFunction) {\n        let intervalMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30000;\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(\"\\uD83D\\uDCBE Auto-save: Enabled with \".concat(intervalMs, \"ms interval\"));\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Only monitor if performance.memory is available (Chrome)\n        if ('memory' in performance) {\n            this.checkInterval = setInterval(()=>{\n                this.checkMemoryUsage();\n            }, 60000); // Check every minute\n        }\n    }\n    checkMemoryUsage() {\n        if ('memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    limit: \"\".concat((memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2), \"MB\")\n                });\n                // Trigger garbage collection if possible\n                if ('gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\")\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if ('memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 100 * 1024 * 1024 // 100MB\n        ;\n        this.criticalThreshold = 200 * 1024 * 1024 // 200MB\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/network-monitor.ts\n"));

/***/ })

});