"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            const currentSessionId = localStorage.getItem(CURRENT_SESSION_KEY);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config) {\n        const autoGeneratedName = this.generateSessionName(config);\n        return this.createNewSession(autoGeneratedName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime if session is currently running\n            let currentRuntime = session.runtime;\n            const startTime = this.sessionStartTimes.get(sessionId);\n            if (startTime && isActive) {\n                // Session is running, update runtime\n                currentRuntime = session.runtime + (Date.now() - startTime);\n                // Reset start time for next interval\n                this.sessionStartTimes.set(sessionId, Date.now());\n            } else if (!isActive && startTime) {\n                // Session stopped, finalize runtime\n                currentRuntime = session.runtime + (Date.now() - startTime);\n                this.sessionStartTimes.delete(sessionId);\n            } else if (isActive && !startTime) {\n                // Session just started, record start time\n                this.sessionStartTimes.set(sessionId, Date.now());\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                localStorage.removeItem(CURRENT_SESSION_KEY);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: session.runtime,\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            localStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.loadSessionsFromStorage();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});