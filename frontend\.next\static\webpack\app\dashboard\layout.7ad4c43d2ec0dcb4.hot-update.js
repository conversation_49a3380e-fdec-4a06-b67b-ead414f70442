"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/AppHeader.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppHeader.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/Logo */ \"(app-pages-browser)/./src/components/shared/Logo.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_backend_status__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/backend-status */ \"(app-pages-browser)/./src/components/ui/backend-status.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AppHeader() {\n    _s();\n    const { logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { saveCurrentSession } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const handleNewSession = ()=>{\n        // Directly open a new tab/window. The new tab will initialize its own independent TradingContext.\n        window.open('/dashboard', '_blank');\n    };\n    const handleSaveSession = ()=>{\n        const success = saveCurrentSession();\n        if (success) {\n            toast({\n                title: \"Session Saved\",\n                description: \"Current session has been saved successfully\"\n            });\n        } else {\n            toast({\n                title: \"Save Failed\",\n                description: \"Failed to save session. Please configure your trading pair first.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Home',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 48,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            href: '/admin',\n            label: 'Admin Panel',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 49,\n                columnNumber: 51\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        useFullName: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_backend_status__WEBPACK_IMPORTED_MODULE_8__.NetworkStatus, {}, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center gap-2 sm:gap-3\",\n                children: [\n                    navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: pathname === item.href ? \"default\" : \"ghost\",\n                            size: \"sm\",\n                            asChild: true,\n                            className: \"\".concat(pathname === item.href ? 'btn-neo' : 'hover:bg-accent/50'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, item.label, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleNewSession,\n                        className: \"hover:bg-accent/50 flex items-center gap-2\",\n                        title: \"Open a new independent trading session in a new tab\" // Added title for clarity\n                        ,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"New Session\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 10\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleLogout,\n                        className: \"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(AppHeader, \"uJ8l7YMOeNgeF1Iy2ZyUrQ89bMM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = AppHeader;\nvar _c;\n$RefreshReg$(_c, \"AppHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppHeader.tsx\n"));

/***/ })

});