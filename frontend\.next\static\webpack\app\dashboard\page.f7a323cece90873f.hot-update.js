"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config, saveCurrentSession, targetPriceRows, orderHistory } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [currentSessionName, setCurrentSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            const updateSessionName = {\n                \"DashboardOrdersPage.useEffect.updateSessionName\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        const session = sessionManager.loadSession(currentSessionId);\n                        if (session) {\n                            setCurrentSessionName(session.name);\n                            return;\n                        }\n                    }\n                    // No session or session not found, generate default name\n                    if (config.crypto1 && config.crypto2) {\n                        const defaultName = \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot');\n                        setCurrentSessionName(defaultName);\n                    } else {\n                        setCurrentSessionName('Crypto 1/Crypto 2 = 0');\n                    }\n                }\n            }[\"DashboardOrdersPage.useEffect.updateSessionName\"];\n            updateSessionName();\n        }\n    }[\"DashboardOrdersPage.useEffect\"], [\n        config.crypto1,\n        config.crypto2,\n        config.tradingMode,\n        sessionManager,\n        targetPriceRows.length,\n        orderHistory.length\n    ]);\n    // Show placeholder text when cryptos are not selected\n    const displayTitle = currentSessionName || (config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot') : \"Crypto 1/Crypto 2 = 0\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayTitle,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"6pc8hGOgzTm9pxcM2Fbv9rPTiwE=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});