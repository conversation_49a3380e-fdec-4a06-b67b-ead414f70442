"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    var _getCurrentSession;\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            setCurrentSessionId(sessionManager.getCurrentSessionId());\n        }\n    }[\"SessionManager.useEffect\"], []);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const success = sessionManager.saveSession(currentSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus === 'Running');\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Session Saved\",\n                description: \"Current session has been saved successfully\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime) return '0m';\n        const minutes = Math.floor(runtime / 60000);\n        const hours = Math.floor(minutes / 60);\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes % 60, \"m\");\n        }\n        return \"\".concat(minutes, \"m\");\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getCurrentSession() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 items-center py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: editingSessionId === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        value: editingName,\n                                                        onChange: (e)=>setEditingName(e.target.value),\n                                                        onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(currentSessionId),\n                                                        className: \"text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleRenameSession(currentSessionId),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: (_getCurrentSession = getCurrentSession()) === null || _getCurrentSession === void 0 ? void 0 : _getCurrentSession.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>{\n                                                            var _getCurrentSession;\n                                                            setEditingSessionId(currentSessionId);\n                                                            setEditingName(((_getCurrentSession = getCurrentSession()) === null || _getCurrentSession === void 0 ? void 0 : _getCurrentSession.name) || '');\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: botSystemStatus === 'Running' ? 'default' : 'secondary',\n                                                children: botSystemStatus === 'Running' ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: formatRuntime(currentSessionId ? sessionManager.getCurrentRuntime(currentSessionId) : 0)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleSaveCurrentSession,\n                                                size: \"sm\",\n                                                className: \"btn-neo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Save\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    sessions.filter((s)=>s.id !== currentSessionId).length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    sessions.filter((s)=>s.id !== currentSessionId).length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: sessions.filter((s)=>s.id !== currentSessionId).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: sessions.filter((s)=>s.id !== currentSessionId).map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(session.runtime)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"1S16dI/F6qbhft23xrQ7/MQUsAU=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});