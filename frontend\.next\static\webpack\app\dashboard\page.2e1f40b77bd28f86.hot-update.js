"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        window.addEventListener('online', this.handleOnline.bind(this));\n        window.addEventListener('offline', this.handleOffline.bind(this));\n        // Listen for visibility change to check connection when tab becomes active\n        document.addEventListener('visibilitychange', ()=>{\n            if (!document.hidden) {\n                this.checkConnection();\n            }\n        });\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true, !this.hasInitialized);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false, !this.hasInitialized);\n    }\n    async checkConnection() {\n        try {\n            // Try to fetch a small resource to verify actual connectivity\n            const response = await fetch('/api/health', {\n                method: 'HEAD',\n                cache: 'no-cache',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            const isConnected = response.ok;\n            if (isConnected !== this.isOnline) {\n                this.isOnline = isConnected;\n                this.notifyListeners(isConnected, !this.hasInitialized);\n                if (isConnected) {\n                    this.lastOnlineTime = Date.now();\n                    this.reconnectAttempts = 0;\n                }\n            }\n            return isConnected;\n        } catch (error) {\n            // If fetch fails, we're likely offline\n            if (this.isOnline) {\n                this.isOnline = false;\n                this.notifyListeners(false);\n            }\n            return false;\n        }\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 30000); // Check every 30 seconds\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(\"\\uD83C\\uDF10 Network: Attempting reconnect \".concat(this.reconnectAttempts, \"/\").concat(this.maxReconnectAttempts, \" in \").concat(delay, \"ms\"));\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n    constructor(){\n        this.isOnline = navigator.onLine;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.hasInitialized = false;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n        // Mark as initialized after a short delay to avoid initial notifications\n        setTimeout(()=>{\n            this.hasInitialized = true;\n        }, 1000);\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        window.addEventListener('beforeunload', ()=>{\n            if (this.saveFunction) {\n                console.log('💾 Auto-save: Saving before page unload');\n                this.saveFunction();\n            }\n        });\n        // Also save on page visibility change (when user switches tabs)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.saveFunction) {\n                console.log('💾 Auto-save: Saving on tab switch');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    enable(saveFunction) {\n        let intervalMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30000;\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(\"\\uD83D\\uDCBE Auto-save: Enabled with \".concat(intervalMs, \"ms interval\"));\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Only monitor if performance.memory is available (Chrome)\n        if ('memory' in performance) {\n            this.checkInterval = setInterval(()=>{\n                this.checkMemoryUsage();\n            }, 60000); // Check every minute\n        }\n    }\n    checkMemoryUsage() {\n        if ('memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    limit: \"\".concat((memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2), \"MB\")\n                });\n                // Trigger garbage collection if possible\n                if ('gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\")\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if ('memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 100 * 1024 * 1024 // 100MB\n        ;\n        this.criticalThreshold = 200 * 1024 * 1024 // 200MB\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/network-monitor.ts\n"));

/***/ })

});