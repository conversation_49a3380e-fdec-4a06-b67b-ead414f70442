"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/ui/crypto-input.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/crypto-input.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoInput: () => (/* binding */ CryptoInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CryptoInput auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CryptoInput(param) {\n    let { label, value, allowedCryptos, onValidCrypto, placeholder = \"Enter crypto symbol\", description, className } = param;\n    _s();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [validationState, setValidationState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [userValidated, setUserValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track if user has validated this crypto\n    const handleCheck = ()=>{\n        const upperCaseInput = inputValue.toUpperCase().trim();\n        if (!upperCaseInput) {\n            setValidationState('invalid');\n            setErrorMessage('Please enter a crypto symbol');\n            return;\n        }\n        if (!allowedCryptos || !Array.isArray(allowedCryptos)) {\n            setValidationState('invalid');\n            setErrorMessage('No allowed cryptocurrencies configured');\n            return;\n        }\n        if (allowedCryptos.includes(upperCaseInput)) {\n            setValidationState('valid');\n            setErrorMessage('');\n            setUserValidated(true); // Mark as user validated\n            onValidCrypto(upperCaseInput);\n        } else {\n            setValidationState('invalid');\n            setErrorMessage(\"\".concat(upperCaseInput, \" is not available. Allowed: \").concat(allowedCryptos.join(', ')));\n        }\n    };\n    const handleInputChange = (e)=>{\n        setInputValue(e.target.value);\n        setValidationState('idle');\n        setErrorMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter') {\n            handleCheck();\n        }\n    };\n    const getValidationIcon = ()=>{\n        switch(validationState){\n            case 'valid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 16\n                }, this);\n            case 'invalid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getValidationColor = ()=>{\n        switch(validationState){\n            case 'valid':\n                return 'border-green-500';\n            case 'invalid':\n                return 'border-red-500';\n            default:\n                return '';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"space-y-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                htmlFor: \"crypto-input-\".concat(label),\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                id: \"crypto-input-\".concat(label),\n                                value: inputValue,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: placeholder,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"pr-8\", getValidationColor())\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            validationState !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                children: getValidationIcon()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleCheck,\n                        variant: \"outline\",\n                        className: \"btn-neo\",\n                        disabled: !inputValue.trim(),\n                        children: \"Check\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            value && userValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-600 font-medium\",\n                        children: [\n                            \"Selected: \",\n                            value\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this),\n            validationState === 'invalid' && errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-2 text-sm text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: errorMessage\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: description\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: [\n                    allowedCryptos && Array.isArray(allowedCryptos) ? allowedCryptos.length : 0,\n                    \" cryptocurrencies available\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(CryptoInput, \"MnFNn7IY0g3RG/36abcLlvksZ20=\");\n_c = CryptoInput;\nvar _c;\n$RefreshReg$(_c, \"CryptoInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/crypto-input.tsx\n"));

/***/ })

});