"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AIContext */ \"(app-pages-browser)/./src/contexts/AIContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/AlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/AlarmConfigModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _components_ui_backend_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/backend-status */ \"(app-pages-browser)/./src/components/ui/backend-status.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // Safely get AI context\n    let aiContext;\n    try {\n        aiContext = (0,_contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__.useAIContext)();\n    } catch (error) {\n        console.warn('AI context not available:', error);\n        aiContext = {\n            suggestion: null,\n            isLoading: false,\n            error: null,\n            getTradingModeSuggestion: ()=>Promise.resolve()\n        };\n    }\n    const { suggestion: aiSuggestion, isLoading: aiLoading, error: aiError, getTradingModeSuggestion } = aiContext;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state\n    const [riskTolerance, setRiskTolerance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [preferredCryptos, setPreferredCryptos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [investmentGoals, setInvestmentGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    const handleAISuggestion = async ()=>{\n        if (!riskTolerance || !preferredCryptos || !investmentGoals) {\n            toast({\n                title: \"AI Suggestion Error\",\n                description: \"Please fill all AI suggestion fields.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        await getTradingModeSuggestion({\n            riskTolerance,\n            preferredCryptocurrencies: preferredCryptos,\n            investmentGoals\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingConfigSidebar.useEffect\": ()=>{\n            if (aiSuggestion) {\n                toast({\n                    title: \"AI Suggestion Received\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.suggestedMode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reason:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                className: \"mt-2 btn-neo\",\n                                onClick: {\n                                    \"TradingConfigSidebar.useEffect\": ()=>dispatch({\n                                            type: 'SET_CONFIG',\n                                            payload: {\n                                                tradingMode: aiSuggestion.suggestedMode === 'Simple Spot' ? 'SimpleSpot' : 'StablecoinSwap'\n                                            }\n                                        })\n                                }[\"TradingConfigSidebar.useEffect\"],\n                                children: \"Apply Suggestion\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    duration: Infinity\n                });\n            }\n            if (aiError) {\n                toast({\n                    title: \"AI Suggestion Error\",\n                    description: aiError,\n                    variant: \"destructive\",\n                    duration: Infinity\n                });\n            }\n        }\n    }[\"TradingConfigSidebar.useEffect\"], [\n        aiSuggestion,\n        aiError,\n        toast,\n        dispatch\n    ]);\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-sidebar-primary\",\n                        children: \"Trading Configuration\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_backend_status__WEBPACK_IMPORTED_MODULE_16__.BackendStatus, {\n                        className: \"text-xs\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS && _lib_types__WEBPACK_IMPORTED_MODULE_14__.AVAILABLE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 81\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" AI Mode Suggestion\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"riskTolerance\",\n                                                    children: \"Risk Tolerance\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: riskTolerance,\n                                                    onValueChange: setRiskTolerance,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"riskTolerance\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select risk tolerance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"low\",\n                                                                    children: \"Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"high\",\n                                                                    children: \"High\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredCryptos\",\n                                                    children: \"Preferred Cryptocurrencies (comma-separated)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"preferredCryptos\",\n                                                    value: preferredCryptos,\n                                                    onChange: (e)=>setPreferredCryptos(e.target.value),\n                                                    placeholder: \"e.g., BTC, ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"investmentGoals\",\n                                                    children: \"Investment Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"investmentGoals\",\n                                                    value: investmentGoals,\n                                                    onChange: (e)=>setInvestmentGoals(e.target.value),\n                                                    placeholder: \"e.g., Long term, Short term profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAISuggestion,\n                                            disabled: aiLoading,\n                                            className: \"w-full btn-neo\",\n                                            children: [\n                                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 31\n                                                }, this),\n                                                \"Get AI Suggestion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_15__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_14__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_18__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_18__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared.\",\n                                        duration: 3000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_13__.AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"VLL9LNLRLp1B1k6hkOPIBw7LnUM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_17__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9UcmFkaW5nQ29uZmlnU2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUM0QjtBQUMzQjtBQUNKO0FBQ0Y7QUFDQTtBQUNNO0FBQ21EO0FBQ047QUFDeEM7QUFDSDtBQUNrQjtBQUNBO0FBQ3VFO0FBQ3BGO0FBQ0k7QUFFL0Qsd0NBQXdDO0FBQ3hDLE1BQU02QiwyQkFBMkI7SUFBQztJQUFRO0lBQVE7Q0FBTTtBQUNxQztBQUNoRDtBQUNaO0FBR2xCLFNBQVNROztJQUN0QixpREFBaUQ7SUFDakQsSUFBSUM7SUFDSixJQUFJO1FBQ0ZBLGlCQUFpQm5DLDJFQUFpQkE7SUFDcEMsRUFBRSxPQUFPb0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtRQUNoRCxxQkFDRSw4REFBQ0U7WUFBTUMsV0FBVTtzQkFDZiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFO29CQUFFRixXQUFVOzhCQUFlOzs7Ozs7Ozs7Ozs7Ozs7O0lBSXBDO0lBRUEsTUFBTSxFQUFFRyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFQyxXQUFXLEVBQUVDLGlCQUFpQkMsc0JBQXNCLEVBQUUsR0FBR1o7SUFDcEcsTUFBTWEsY0FBY0osb0JBQW9CO0lBQ3hDLE1BQU1LLGlCQUFpQkwsb0JBQW9CO0lBRTNDLHdCQUF3QjtJQUN4QixJQUFJTTtJQUNKLElBQUk7UUFDRkEsWUFBWWpELGlFQUFZQTtJQUMxQixFQUFFLE9BQU9tQyxPQUFPO1FBQ2RDLFFBQVFjLElBQUksQ0FBQyw2QkFBNkJmO1FBQzFDYyxZQUFZO1lBQUVFLFlBQVk7WUFBTUMsV0FBVztZQUFPakIsT0FBTztZQUFNa0IsMEJBQTBCLElBQU1DLFFBQVFDLE9BQU87UUFBRztJQUNuSDtJQUVBLE1BQU0sRUFBRUosWUFBWUssWUFBWSxFQUFFSixXQUFXSyxTQUFTLEVBQUV0QixPQUFPdUIsT0FBTyxFQUFFTCx3QkFBd0IsRUFBRSxHQUFHSjtJQUNyRyxNQUFNLEVBQUVVLEtBQUssRUFBRSxHQUFHNUIsMkRBQVFBO0lBRTFCLE1BQU0sQ0FBQzZCLG1CQUFtQkMscUJBQXFCLEdBQUdoRSwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNpRSxrQkFBa0JDLG9CQUFvQixHQUFHbEUsK0NBQVFBLENBQUM7SUFFekQsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQ21FLGVBQWVDLGlCQUFpQixHQUFHcEUsK0NBQVFBLENBQVM7SUFDM0QsTUFBTSxDQUFDcUUsa0JBQWtCQyxvQkFBb0IsR0FBR3RFLCtDQUFRQSxDQUFTO0lBQ2pFLE1BQU0sQ0FBQ3VFLGlCQUFpQkMsbUJBQW1CLEdBQUd4RSwrQ0FBUUEsQ0FBUztJQUcvRCxNQUFNeUUsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFFLEdBQUdKLEVBQUVLLE1BQU07UUFDL0MsSUFBSUM7UUFFSixJQUFJSCxTQUFTLFlBQVk7WUFDdkJHLE1BQU1GO1FBQ1IsT0FBTyxJQUFJRCxTQUFTLFVBQVU7WUFDNUIsb0RBQW9EO1lBQ3BELElBQUlELFVBQVUsTUFBTUEsVUFBVSxRQUFRQSxVQUFVSyxXQUFXO2dCQUN6REQsTUFBTTtZQUNSLE9BQU87Z0JBQ0wsTUFBTUUsU0FBU0MsV0FBV1A7Z0JBQzFCSSxNQUFNSSxNQUFNRixVQUFVLElBQUlBO1lBQzVCO1FBQ0YsT0FBTztZQUNMRixNQUFNSjtRQUNSO1FBRUEvQixTQUFTO1lBQUVnQyxNQUFNO1lBQWNRLFNBQVM7Z0JBQUUsQ0FBQ1YsS0FBSyxFQUFFSztZQUFJO1FBQUU7SUFDMUQ7SUFFQSxNQUFNTSxxQkFBcUIsQ0FBQ1gsTUFBY0M7UUFDeEMvQixTQUFTO1lBQUVnQyxNQUFNO1lBQWNRLFNBQVM7Z0JBQUUsQ0FBQ1YsS0FBSyxFQUFFQztZQUFNO1FBQUU7UUFDMUQsSUFBSUQsU0FBUyxXQUFXO1lBQ3RCLCtEQUErRDtZQUMvRCxNQUFNWSxjQUFjakUsZ0VBQXVCLENBQUNzRCxNQUE4QyxJQUFJaEQsNEJBQTRCO2dCQUFDO2dCQUFRO2dCQUFRO2FBQU07WUFDakosSUFBSSxDQUFDZ0IsT0FBTzRDLE9BQU8sSUFBSSxDQUFDQyxNQUFNQyxPQUFPLENBQUNILGdCQUFnQixDQUFDQSxZQUFZSSxRQUFRLENBQUMvQyxPQUFPNEMsT0FBTyxHQUFHO2dCQUMzRjNDLFNBQVM7b0JBQUVnQyxNQUFNO29CQUFjUSxTQUFTO3dCQUFFRyxTQUFTRCxXQUFXLENBQUMsRUFBRSxJQUFJO29CQUFPO2dCQUFFLElBQUksc0NBQXNDO1lBQzFIO1FBQ0Y7SUFDRjtJQUVBLE1BQU1LLHlCQUF5QixDQUFDQztRQUM5QmhELFNBQVM7WUFBRWdDLE1BQU07WUFBY1EsU0FBUztnQkFBRVMsU0FBU0Q7WUFBTztRQUFFO1FBQzVELDJEQUEyRDtRQUMzRCxJQUFJakQsT0FBT21ELFdBQVcsS0FBSyxrQkFBa0I7WUFDM0MsNkZBQTZGO1lBQzdGLE1BQU1DLGlCQUFpQixDQUFDeEUsd0RBQWVBLElBQUk7Z0JBQUM7Z0JBQU87Z0JBQU87Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQVU7Z0JBQU87Z0JBQVE7Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQVE7YUFBTSxFQUFFeUUsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNTDtZQUNyTyxJQUFJLENBQUNHLGVBQWVMLFFBQVEsQ0FBQy9DLE9BQU80QyxPQUFPLEtBQUs1QyxPQUFPNEMsT0FBTyxLQUFLSyxRQUFRO2dCQUN6RWhELFNBQVM7b0JBQUVnQyxNQUFNO29CQUFjUSxTQUFTO3dCQUFFRyxTQUFTUSxjQUFjLENBQUMsRUFBRTtvQkFBQztnQkFBRTtZQUN6RTtRQUNGLE9BQU87WUFDTCw4REFBOEQ7WUFDOUQsTUFBTUEsaUJBQWlCdkUsd0RBQWVBLElBQUk7Z0JBQUM7Z0JBQVE7Z0JBQU87Z0JBQVE7Z0JBQVM7Z0JBQVE7YUFBTTtZQUN6RixJQUFJLENBQUN1RSxlQUFlTCxRQUFRLENBQUMvQyxPQUFPNEMsT0FBTyxHQUFHO2dCQUM1QzNDLFNBQVM7b0JBQUVnQyxNQUFNO29CQUFjUSxTQUFTO3dCQUFFRyxTQUFTUSxjQUFjLENBQUMsRUFBRTtvQkFBQztnQkFBRTtZQUN6RTtRQUNGO0lBQ0Y7SUFFQSxNQUFNRyx5QkFBeUIsQ0FBQ047UUFDOUIsb0VBQW9FO1FBQ3BFLElBQUlqRCxPQUFPbUQsV0FBVyxLQUFLLG9CQUFvQkYsV0FBV2pELE9BQU9rRCxPQUFPLEVBQUU7WUFDeEUsZ0VBQWdFO1lBQ2hFO1FBQ0Y7UUFDQWpELFNBQVM7WUFBRWdDLE1BQU07WUFBY1EsU0FBUztnQkFBRUcsU0FBU0s7WUFBTztRQUFFO0lBQzlEO0lBRUEsTUFBTU8sMEJBQTBCLENBQUN0QjtRQUMvQixNQUFNdUIsVUFBdUJ2QixVQUFVLG1CQUFtQjtRQUUxRCw4Q0FBOEM7UUFDOUMsSUFBSXdCO1FBQ0osSUFBSUQsWUFBWSxrQkFBa0I7WUFDaEMsNkZBQTZGO1lBQzdGLE1BQU1MLGlCQUFpQixDQUFDeEUsd0RBQWVBLElBQUk7Z0JBQUM7Z0JBQU87Z0JBQU87Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQVU7Z0JBQU87Z0JBQVE7Z0JBQU87Z0JBQU87Z0JBQVE7Z0JBQVE7Z0JBQU87Z0JBQVE7YUFBTSxFQUFFeUUsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNdEQsT0FBT2tELE9BQU87WUFDblBRLGFBQWFOLGNBQWMsQ0FBQyxFQUFFO1FBQ2hDLE9BQU87WUFDTCw4REFBOEQ7WUFDOUQsTUFBTUEsaUJBQWlCdkUsd0RBQWVBLElBQUk7Z0JBQUM7Z0JBQVE7Z0JBQU87Z0JBQVE7Z0JBQVM7Z0JBQVE7YUFBTTtZQUN6RjZFLGFBQWFOLGNBQWMsQ0FBQyxFQUFFO1FBQ2hDO1FBRUFuRCxTQUFTO1lBQUVnQyxNQUFNO1lBQWNRLFNBQVM7Z0JBQUVVLGFBQWFNO2dCQUFTYixTQUFTYztZQUFXO1FBQUU7SUFDeEY7SUFFQSxNQUFNQywwQkFBMEIsQ0FBQ0MsV0FBc0U1QjtRQUNyRyxJQUFJNkIsVUFBVXRCLFdBQVdQO1FBQ3pCLElBQUlRLE1BQU1xQixVQUFVQSxVQUFVO1FBQzlCLElBQUlBLFVBQVUsR0FBR0EsVUFBVTtRQUMzQixJQUFJQSxVQUFVLEtBQUtBLFVBQVU7UUFFN0IsSUFBSUQsY0FBYyw2QkFBNkI7WUFDN0MzRCxTQUFTO2dCQUFFZ0MsTUFBTTtnQkFBY1EsU0FBUztvQkFBRXFCLDJCQUEyQkQ7b0JBQVNFLDJCQUEyQixNQUFNRjtnQkFBUTtZQUFFO1FBQzNILE9BQU87WUFDTDVELFNBQVM7Z0JBQUVnQyxNQUFNO2dCQUFjUSxTQUFTO29CQUFFc0IsMkJBQTJCRjtvQkFBU0MsMkJBQTJCLE1BQU1EO2dCQUFRO1lBQUU7UUFDM0g7SUFDRjtJQUVBLE1BQU1HLHFCQUFxQjtRQUN6QixJQUFJLENBQUN6QyxpQkFBaUIsQ0FBQ0Usb0JBQW9CLENBQUNFLGlCQUFpQjtZQUMzRFQsTUFBTTtnQkFBRStDLE9BQU87Z0JBQXVCQyxhQUFhO2dCQUF5Q0MsU0FBUztZQUFjO1lBQ25IO1FBQ0Y7UUFDQSxNQUFNdkQseUJBQXlCO1lBQUVXO1lBQWU2QywyQkFBMkIzQztZQUFrQkU7UUFBZ0I7SUFDL0c7SUFJQXRFLGdEQUFTQTswQ0FBQztZQUNSLElBQUkwRCxjQUFjO2dCQUNoQkcsTUFBTTtvQkFDSitDLE9BQU87b0JBQ1BDLDJCQUNFLDhEQUFDcEU7OzBDQUNDLDhEQUFDQzs7a0RBQUUsOERBQUNzRTtrREFBTzs7Ozs7O29DQUFjO29DQUFFdEQsYUFBYXVELGFBQWE7Ozs7Ozs7MENBQ3JELDhEQUFDdkU7O2tEQUFFLDhEQUFDc0U7a0RBQU87Ozs7OztvQ0FBZ0I7b0NBQUV0RCxhQUFhd0QsTUFBTTs7Ozs7OzswQ0FDaEQsOERBQUMvRyx5REFBTUE7Z0NBQUNnSCxNQUFLO2dDQUFLM0UsV0FBVTtnQ0FBZTRFLE9BQU87c0VBQUUsSUFBTXhFLFNBQVM7NENBQUVnQyxNQUFNOzRDQUFjUSxTQUFTO2dEQUFFVSxhQUFhcEMsYUFBYXVELGFBQWEsS0FBSyxnQkFBZ0IsZUFBZTs0Q0FBaUI7d0NBQUM7OzBDQUFJOzs7Ozs7Ozs7Ozs7b0JBS3pNSSxVQUFVQztnQkFDWjtZQUNGO1lBQ0EsSUFBSTFELFNBQVM7Z0JBQ1hDLE1BQU07b0JBQUUrQyxPQUFPO29CQUF1QkMsYUFBYWpEO29CQUFTa0QsU0FBUztvQkFBZU8sVUFBVUM7Z0JBQVM7WUFDekc7UUFDRjt5Q0FBRztRQUFDNUQ7UUFBY0U7UUFBU0M7UUFBT2pCO0tBQVM7SUFHM0MsMEJBQTBCO0lBQzFCLE1BQU0yRSxpQkFBaUJuRywwREFBaUJBLElBQUksRUFBRTtJQUM5QyxNQUFNb0csaUJBQWlCN0UsT0FBT21ELFdBQVcsS0FBSyxlQUN6Q3pFLGdFQUF1QixDQUFDc0IsT0FBT2tELE9BQU8sQ0FBeUMsSUFBSWxFLDRCQUE0QjtRQUFDO1FBQVE7UUFBUTtLQUFNLEdBQ3ZJLENBQUNQLDBEQUFpQkEsSUFBSSxFQUFFLEVBQUU0RSxNQUFNLENBQUNDLENBQUFBLElBQUtBLE1BQU10RCxPQUFPa0QsT0FBTyxHQUFHLHFEQUFxRDtJQUV0SCwwQ0FBMEM7SUFDMUN2RCxRQUFRbUYsR0FBRyxDQUFDLHVDQUF1Q3JHLDBEQUFpQkEsYUFBakJBLDBEQUFpQkEsdUJBQWpCQSwwREFBaUJBLENBQUVzRyxNQUFNO0lBQzVFcEYsUUFBUW1GLEdBQUcsQ0FBQyxvQ0FBb0NGLGVBQWVHLE1BQU07SUFDckVwRixRQUFRbUYsR0FBRyxDQUFDLCtCQUErQnJHLDBEQUFpQkEsYUFBakJBLDBEQUFpQkEsdUJBQWpCQSwwREFBaUJBLENBQUV1RyxLQUFLLENBQUMsR0FBRztJQUN2RXJGLFFBQVFtRixHQUFHLENBQUMsOEJBQThCbEcsd0RBQWVBO0lBQ3pEZSxRQUFRbUYsR0FBRyxDQUFDLDhCQUE4QmpHLHdEQUFlQTtJQUV6RCxxQkFDRSw4REFBQ2U7UUFBTUMsV0FBVTs7MEJBQ2YsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ29GO3dCQUFHcEYsV0FBVTtrQ0FBMEM7Ozs7OztrQ0FDeEQsOERBQUNkLHlFQUFhQTt3QkFBQ2MsV0FBVTs7Ozs7Ozs7Ozs7OzBCQUUzQiw4REFBQ3hCLG1FQUFVQTtnQkFBQ3dCLFdBQVU7MEJBQ3BCLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUM1QixxREFBSUE7NEJBQUM0QixXQUFVOzs4Q0FDZCw4REFBQzFCLDJEQUFVQTs4Q0FDVCw0RUFBQ0MsMERBQVNBO3dDQUFDeUIsV0FBVTtrREFBaUM7Ozs7Ozs7Ozs7OzhDQUV4RCw4REFBQzNCLDREQUFXQTtvQ0FBQzJCLFdBQVU7O3NEQUNyQiw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDbEMsNkRBQVFBO29EQUNQdUgsSUFBRztvREFDSGhELFNBQVNsQyxPQUFPbUQsV0FBVyxLQUFLO29EQUNoQ2dDLGlCQUFpQjNCOzs7Ozs7OERBRW5CLDhEQUFDOUYsdURBQUtBO29EQUFDMEgsU0FBUTtvREFBdUJ2RixXQUFVOzhEQUE2Rjs7Ozs7Ozs7Ozs7O3NEQUkvSSw4REFBQ0U7NENBQUVGLFdBQVU7O2dEQUFnQztnREFDNUJHLE9BQU9tRCxXQUFXLEtBQUssZUFBZSxnQkFBZ0I7Ozs7Ozs7d0NBRXRFbkQsT0FBT21ELFdBQVcsS0FBSyxrQ0FDckIsOERBQUNyRDs7OERBQ0EsOERBQUNwQyx1REFBS0E7b0RBQUMwSCxTQUFROzhEQUFzQjs7Ozs7OzhEQUNyQyw4REFBQ3hILHlEQUFNQTtvREFBQ21FLE1BQUs7b0RBQXNCQyxPQUFPaEMsT0FBT3FGLG1CQUFtQjtvREFBRUMsZUFBZSxDQUFDbEQsTUFBUU0sbUJBQW1CLHVCQUF1Qk47O3NFQUN0SSw4REFBQ3JFLGdFQUFhQTs0REFBQ21ILElBQUc7c0VBQXNCLDRFQUFDbEgsOERBQVdBO2dFQUFDdUgsYUFBWTs7Ozs7Ozs7Ozs7c0VBQ2pFLDhEQUFDMUgsZ0VBQWFBOzREQUFDZ0MsV0FBVTtzRUFDdEJsQiw4REFBcUJBLElBQUlBLDhEQUFxQkEsQ0FBQzZHLEdBQUcsQ0FBQ0MsQ0FBQUEsbUJBQU0sOERBQUMzSCw2REFBVUE7b0VBQVVrRSxPQUFPeUQ7OEVBQUtBO21FQUFoQkE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBU3ZGLDhEQUFDeEgscURBQUlBOzRCQUFDNEIsV0FBVTs7OENBQ2QsOERBQUMxQiwyREFBVUE7OENBQ1QsNEVBQUNDLDBEQUFTQTt3Q0FBQ3lCLFdBQVU7OzBEQUFtRCw4REFBQ1gsdUhBQVNBO2dEQUFDVyxXQUFVOzs7Ozs7NENBQThCOzs7Ozs7Ozs7Ozs7OENBRTdILDhEQUFDM0IsNERBQVdBO29DQUFDMkIsV0FBVTs7c0RBQ3JCLDhEQUFDQzs7OERBQ0MsOERBQUNwQyx1REFBS0E7b0RBQUMwSCxTQUFROzhEQUFnQjs7Ozs7OzhEQUMvQiw4REFBQ3hILHlEQUFNQTtvREFBQ29FLE9BQU9UO29EQUFlK0QsZUFBZTlEOztzRUFDM0MsOERBQUN6RCxnRUFBYUE7NERBQUNtSCxJQUFHO3NFQUFnQiw0RUFBQ2xILDhEQUFXQTtnRUFBQ3VILGFBQVk7Ozs7Ozs7Ozs7O3NFQUMzRCw4REFBQzFILGdFQUFhQTs7OEVBQ1osOERBQUNDLDZEQUFVQTtvRUFBQ2tFLE9BQU07OEVBQU07Ozs7Ozs4RUFDeEIsOERBQUNsRSw2REFBVUE7b0VBQUNrRSxPQUFNOzhFQUFTOzs7Ozs7OEVBQzNCLDhEQUFDbEUsNkRBQVVBO29FQUFDa0UsT0FBTTs4RUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUkvQiw4REFBQ2xDOzs4REFDQyw4REFBQ3BDLHVEQUFLQTtvREFBQzBILFNBQVE7OERBQW1COzs7Ozs7OERBQ2xDLDhEQUFDM0gsdURBQUtBO29EQUFDeUgsSUFBRztvREFBbUJsRCxPQUFPUDtvREFBa0JpRSxVQUFVLENBQUM1RCxJQUFNSixvQkFBb0JJLEVBQUVLLE1BQU0sQ0FBQ0gsS0FBSztvREFBR3VELGFBQVk7Ozs7Ozs7Ozs7OztzREFFMUgsOERBQUN6Rjs7OERBQ0MsOERBQUNwQyx1REFBS0E7b0RBQUMwSCxTQUFROzhEQUFrQjs7Ozs7OzhEQUNqQyw4REFBQzNILHVEQUFLQTtvREFBQ3lILElBQUc7b0RBQWtCbEQsT0FBT0w7b0RBQWlCK0QsVUFBVSxDQUFDNUQsSUFBTUYsbUJBQW1CRSxFQUFFSyxNQUFNLENBQUNILEtBQUs7b0RBQUd1RCxhQUFZOzs7Ozs7Ozs7Ozs7c0RBRXZILDhEQUFDL0gseURBQU1BOzRDQUFDaUgsU0FBU1Q7NENBQW9CMkIsVUFBVTNFOzRDQUFXbkIsV0FBVTs7Z0RBQ2pFbUIsMkJBQWEsOERBQUMvQix1SEFBT0E7b0RBQUNZLFdBQVU7Ozs7OztnREFBK0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3RFLDhEQUFDNUIscURBQUlBOzRCQUFDNEIsV0FBVTs7OENBQ2QsOERBQUMxQiwyREFBVUE7OENBQUMsNEVBQUNDLDBEQUFTQTt3Q0FBQ3lCLFdBQVU7a0RBQWlDOzs7Ozs7Ozs7Ozs4Q0FDbEUsOERBQUMzQiw0REFBV0E7b0NBQUMyQixXQUFVOztzREFDckIsOERBQUNmLHFFQUFXQTs0Q0FDVjhHLE9BQU07NENBQ041RCxPQUFPaEMsT0FBT2tELE9BQU87NENBQ3JCMkMsZ0JBQWdCakgsd0RBQWVBLElBQUk7Z0RBQUM7Z0RBQU87Z0RBQU87Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQVU7Z0RBQU87Z0RBQVE7Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQVE7NkNBQU07NENBQ3pNa0gsZUFBZTlDOzRDQUNmdUMsYUFBWTs0Q0FDWnJCLGFBQVk7Ozs7OztzREFFZCw4REFBQ3BGLHFFQUFXQTs0Q0FDVjhHLE9BQU81RixPQUFPbUQsV0FBVyxLQUFLLG1CQUFtQixhQUFhOzRDQUM5RG5CLE9BQU9oQyxPQUFPNEMsT0FBTzs0Q0FDckJpRCxnQkFBZ0I3RixPQUFPbUQsV0FBVyxLQUFLLG1CQUNuQyxDQUFDdkUsd0RBQWVBLElBQUk7Z0RBQUM7Z0RBQU87Z0RBQU87Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQVU7Z0RBQU87Z0RBQVE7Z0RBQU87Z0RBQU87Z0RBQVE7Z0RBQVE7Z0RBQU87Z0RBQVE7NkNBQU0sRUFBRXlFLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTXRELE9BQU9rRCxPQUFPLElBQzNOckUsd0RBQWVBLElBQUk7Z0RBQUM7Z0RBQVE7Z0RBQU87Z0RBQVE7Z0RBQVM7Z0RBQVE7NkNBQU07NENBRXZFaUgsZUFBZXZDOzRDQUNmZ0MsYUFBYXZGLE9BQU9tRCxXQUFXLEtBQUssbUJBQW1CLHdCQUF3Qjs0Q0FDL0VlLGFBQWFsRSxPQUFPbUQsV0FBVyxLQUFLLG1CQUFtQiwyQ0FBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNeEcsOERBQUNsRixxREFBSUE7NEJBQUM0QixXQUFVOzs4Q0FDZCw4REFBQzFCLDJEQUFVQTs4Q0FBQyw0RUFBQ0MsMERBQVNBO3dDQUFDeUIsV0FBVTtrREFBaUM7Ozs7Ozs7Ozs7OzhDQUNsRSw4REFBQzNCLDREQUFXQTtvQ0FBQzJCLFdBQVU7O3dDQUNwQjs0Q0FDQztnREFBRWtDLE1BQU07Z0RBQVc2RCxPQUFPO2dEQUF1QjNELE1BQU07Z0RBQVU4RCxNQUFNOzRDQUFPOzRDQUM5RTtnREFBRWhFLE1BQU07Z0RBQWM2RCxPQUFPO2dEQUFjM0QsTUFBTTtnREFBVThELE1BQU07NENBQVE7NENBQ3pFO2dEQUFFaEUsTUFBTTtnREFBYTZELE9BQU87Z0RBQWtCM0QsTUFBTTtnREFBVThELE1BQU07NENBQUk7NENBQ3hFO2dEQUFFaEUsTUFBTTtnREFBbUI2RCxPQUFPO2dEQUFjM0QsTUFBTTtnREFBVThELE1BQU07NENBQU87eUNBQzlFLENBQUNQLEdBQUcsQ0FBQ1EsQ0FBQUEsc0JBQ0osOERBQUNsRzs7a0VBQ0MsOERBQUNwQyx1REFBS0E7d0RBQUMwSCxTQUFTWSxNQUFNakUsSUFBSTtrRUFBR2lFLE1BQU1KLEtBQUs7Ozs7OztrRUFDeEMsOERBQUNuSSx1REFBS0E7d0RBQ0p5SCxJQUFJYyxNQUFNakUsSUFBSTt3REFDZEEsTUFBTWlFLE1BQU1qRSxJQUFJO3dEQUNoQkUsTUFBTStELE1BQU0vRCxJQUFJO3dEQUNoQkQsT0FBT2hDLE1BQU0sQ0FBQ2dHLE1BQU1qRSxJQUFJLENBQXdCO3dEQUNoRDJELFVBQVU3RDt3REFDVmtFLE1BQU1DLE1BQU1ELElBQUk7d0RBQ2hCRSxLQUFJOzs7Ozs7OytDQVRFRCxNQUFNakUsSUFBSTs7Ozs7c0RBYXRCLDhEQUFDakM7OzhEQUNDLDhEQUFDcEMsdURBQUtBOzhEQUFDOzs7Ozs7OERBQ1AsOERBQUNvQztvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUNDOzs4RUFDQyw4REFBQ3BDLHVEQUFLQTtvRUFBQzBILFNBQVE7b0VBQTRCdkYsV0FBVTs7d0VBQVdHLE9BQU9rRCxPQUFPO3dFQUFDOzs7Ozs7OzhFQUMvRSw4REFBQ3pGLHVEQUFLQTtvRUFBQ3lILElBQUc7b0VBQTRCakQsTUFBSztvRUFBU0QsT0FBT2hDLE9BQU84RCx5QkFBeUI7b0VBQUU0QixVQUFVLENBQUM1RCxJQUFNNkIsd0JBQXdCLDZCQUE2QjdCLEVBQUVLLE1BQU0sQ0FBQ0gsS0FBSztvRUFBR2lFLEtBQUk7b0VBQUlDLEtBQUk7Ozs7Ozs7Ozs7OztzRUFFbE0sOERBQUNwRzs7OEVBQ0MsOERBQUNwQyx1REFBS0E7b0VBQUMwSCxTQUFRO29FQUE0QnZGLFdBQVU7O3dFQUFXRyxPQUFPNEMsT0FBTzt3RUFBQzs7Ozs7Ozs4RUFDL0UsOERBQUNuRix1REFBS0E7b0VBQUN5SCxJQUFHO29FQUE0QmpELE1BQUs7b0VBQVNELE9BQU9oQyxPQUFPK0QseUJBQXlCO29FQUFFMkIsVUFBVSxDQUFDNUQsSUFBTTZCLHdCQUF3Qiw2QkFBNkI3QixFQUFFSyxNQUFNLENBQUNILEtBQUs7b0VBQUdpRSxLQUFJO29FQUFJQyxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTOU0sOERBQUNwRztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUN2QixnRUFBU0E7d0JBQUN1QixXQUFVOzs7Ozs7a0NBRXJCLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFXTiwrQ0FBRUEsQ0FDZCxnSEFDQWUsY0FBYyx5Q0FBeUM7O29DQUd4REEsNEJBQWMsOERBQUNuQix1SEFBS0E7d0NBQUNVLFdBQVU7Ozs7OytDQUFlVSwrQkFBaUIsOERBQUN0Qix1SEFBT0E7d0NBQUNZLFdBQVU7Ozs7OzZEQUFpQyw4REFBQ1QsdUhBQVFBO3dDQUFDUyxXQUFVOzs7Ozs7b0NBQWE7b0NBRXhJUyxjQUFjLFlBQWFDLGlCQUFpQixlQUFlOzs7Ozs7OzBDQUUxRSw4REFBQy9DLHlEQUFNQTtnQ0FBQ2lILFNBQVMsSUFBTXJELHFCQUFxQjtnQ0FBT3ZCLFdBQVU7MENBQXlCOzs7Ozs7MENBQ3RGLDhEQUFDckMseURBQU1BO2dDQUFDaUgsU0FBUyxJQUFNbkQsb0JBQW9CO2dDQUFPekIsV0FBVTswQ0FBeUI7Ozs7OzswQ0FDckYsOERBQUNyQyx5REFBTUE7Z0NBQ0xpSCxTQUFTO29DQUNQLElBQUluRSxhQUFhO3dDQUNmTCxTQUFTOzRDQUFFZ0MsTUFBTTt3Q0FBa0I7b0NBQ3JDLE9BQU87d0NBQ0xoQyxTQUFTOzRDQUFFZ0MsTUFBTTt3Q0FBNEI7b0NBQy9DO2dDQUNGO2dDQUNBcEMsV0FBV04sK0NBQUVBLENBQUMsa0JBQWtCZSxlQUFlQyxpQkFBaUIsMkNBQTJDO2dDQUMzR29GLFVBQVVwRjs7b0NBRVRELDRCQUFjLDhEQUFDbkIsdUhBQUtBO3dDQUFDVSxXQUFVOzs7OzsrQ0FBZVUsK0JBQWlCLDhEQUFDdEIsdUhBQU9BO3dDQUFDWSxXQUFVOzs7Ozs2REFBaUMsOERBQUNULHVIQUFRQTt3Q0FBQ1MsV0FBVTs7Ozs7O29DQUN2SVMsY0FBYyxhQUFjQyxpQkFBaUIsa0JBQWtCOzs7Ozs7OzBDQUVsRSw4REFBQy9DLHlEQUFNQTtnQ0FDTGlILFNBQVM7b0NBQ1B4RSxTQUFTO3dDQUFFZ0MsTUFBTTtvQ0FBbUI7b0NBQ3BDZixNQUFNO3dDQUNKK0MsT0FBTzt3Q0FDUEMsYUFBYTt3Q0FDYlEsVUFBVTtvQ0FDWjtnQ0FDRjtnQ0FDQVAsU0FBUTtnQ0FDUnRFLFdBQVU7Z0NBQ1Y4RixVQUFVcEY7O2tEQUVWLDhEQUFDbEIsdUhBQVNBO3dDQUFDUSxXQUFVOzs7Ozs7b0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0zQyw4REFBQ3RCLGtGQUFnQkE7Z0JBQUM0SCxRQUFRaEY7Z0JBQW1CaUYsU0FBUyxJQUFNaEYscUJBQXFCO2dCQUFRaUYsbUJBQW1CaEc7Ozs7OzswQkFDNUcsOERBQUM3QixrRkFBZ0JBO2dCQUFDMkgsUUFBUTlFO2dCQUFrQitFLFNBQVMsSUFBTTlFLG9CQUFvQjs7Ozs7Ozs7Ozs7O0FBR3JGO0dBaFh3QjlCOztRQThCSkYsdURBQVFBOzs7S0E5QkpFIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXFRyYWRpbmdDb25maWdTaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFkaW5nQ29udGV4dCwgQm90U3lzdGVtU3RhdHVzIH0gZnJvbSAnQC9jb250ZXh0cy9UcmFkaW5nQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VBSUNvbnRleHQgfSBmcm9tICdAL2NvbnRleHRzL0FJQ29udGV4dCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIjtcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jaGVja2JveFwiO1xuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2VsZWN0XCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEnO1xuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvcic7XG5pbXBvcnQgeyBUYXJnZXRQcmljZU1vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL21vZGFscy9UYXJnZXRQcmljZU1vZGFsJztcbmltcG9ydCB7IEFsYXJtQ29uZmlnTW9kYWwgfSBmcm9tICdAL2NvbXBvbmVudHMvbW9kYWxzL0FsYXJtQ29uZmlnTW9kYWwnO1xuaW1wb3J0IHsgQVZBSUxBQkxFX0NSWVBUT1MsIEFWQUlMQUJMRV9RVU9URVNfU0lNUExFLCBBVkFJTEFCTEVfU1RBQkxFQ09JTlMsIFRyYWRpbmdNb2RlLCBBTExPV0VEX0NSWVBUTzEsIEFMTE9XRURfQ1JZUFRPMiB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IENyeXB0b0lucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NyeXB0by1pbnB1dCc7XG5pbXBvcnQgeyBCYWNrZW5kU3RhdHVzIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhY2tlbmQtc3RhdHVzJztcblxuLy8gRGVmaW5lIGxvY2FsbHkgdG8gYXZvaWQgaW1wb3J0IGlzc3Vlc1xuY29uc3QgREVGQVVMVF9RVU9URV9DVVJSRU5DSUVTID0gW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl07XG5pbXBvcnQgeyBMb2FkZXIyLCBBbGVydFRyaWFuZ2xlLCBMaWdodGJ1bGIsIFBvd2VyLCBQb3dlck9mZiwgUm90YXRlQ2N3IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVHJhZGluZ0NvbmZpZ1NpZGViYXIoKSB7XG4gIC8vIFNhZmVseSBnZXQgdHJhZGluZyBjb250ZXh0IHdpdGggZXJyb3IgaGFuZGxpbmdcbiAgbGV0IHRyYWRpbmdDb250ZXh0O1xuICB0cnkge1xuICAgIHRyYWRpbmdDb250ZXh0ID0gdXNlVHJhZGluZ0NvbnRleHQoKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdUcmFkaW5nIGNvbnRleHQgbm90IGF2YWlsYWJsZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIChcbiAgICAgIDxhc2lkZSBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy04MCBsZzp3LTk2IGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgcC00IGJvcmRlci1yLTIgYm9yZGVyLXNpZGViYXItYm9yZGVyIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj5UcmFkaW5nIGNvbnRleHQgbm90IGF2YWlsYWJsZS4gUGxlYXNlIHJlZnJlc2ggdGhlIHBhZ2UuPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYXNpZGU+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IHsgY29uZmlnLCBkaXNwYXRjaCwgYm90U3lzdGVtU3RhdHVzLCBhcHBTZXR0aW5ncywgc2V0VGFyZ2V0UHJpY2VzOiBjb250ZXh0U2V0VGFyZ2V0UHJpY2VzIH0gPSB0cmFkaW5nQ29udGV4dDtcbiAgY29uc3QgaXNCb3RBY3RpdmUgPSBib3RTeXN0ZW1TdGF0dXMgPT09ICdSdW5uaW5nJztcbiAgY29uc3QgaXNCb3RXYXJtaW5nVXAgPSBib3RTeXN0ZW1TdGF0dXMgPT09ICdXYXJtaW5nVXAnO1xuXG4gIC8vIFNhZmVseSBnZXQgQUkgY29udGV4dFxuICBsZXQgYWlDb250ZXh0O1xuICB0cnkge1xuICAgIGFpQ29udGV4dCA9IHVzZUFJQ29udGV4dCgpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2FybignQUkgY29udGV4dCBub3QgYXZhaWxhYmxlOicsIGVycm9yKTtcbiAgICBhaUNvbnRleHQgPSB7IHN1Z2dlc3Rpb246IG51bGwsIGlzTG9hZGluZzogZmFsc2UsIGVycm9yOiBudWxsLCBnZXRUcmFkaW5nTW9kZVN1Z2dlc3Rpb246ICgpID0+IFByb21pc2UucmVzb2x2ZSgpIH07XG4gIH1cblxuICBjb25zdCB7IHN1Z2dlc3Rpb246IGFpU3VnZ2VzdGlvbiwgaXNMb2FkaW5nOiBhaUxvYWRpbmcsIGVycm9yOiBhaUVycm9yLCBnZXRUcmFkaW5nTW9kZVN1Z2dlc3Rpb24gfSA9IGFpQ29udGV4dDtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcblxuICBjb25zdCBbaXNUYXJnZXRNb2RhbE9wZW4sIHNldElzVGFyZ2V0TW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQWxhcm1Nb2RhbE9wZW4sIHNldElzQWxhcm1Nb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEFJIFN1Z2dlc3Rpb24gZm9ybSBzdGF0ZVxuICBjb25zdCBbcmlza1RvbGVyYW5jZSwgc2V0Umlza1RvbGVyYW5jZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwibWVkaXVtXCIpO1xuICBjb25zdCBbcHJlZmVycmVkQ3J5cHRvcywgc2V0UHJlZmVycmVkQ3J5cHRvc10gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xuICBjb25zdCBbaW52ZXN0bWVudEdvYWxzLCBzZXRJbnZlc3RtZW50R29hbHNdID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKTtcblxuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSwgdHlwZSwgY2hlY2tlZCB9ID0gZS50YXJnZXQ7XG4gICAgbGV0IHZhbDogYW55O1xuXG4gICAgaWYgKHR5cGUgPT09ICdjaGVja2JveCcpIHtcbiAgICAgIHZhbCA9IGNoZWNrZWQ7XG4gICAgfSBlbHNlIGlmICh0eXBlID09PSAnbnVtYmVyJykge1xuICAgICAgLy8gSGFuZGxlIGVtcHR5IHN0cmluZyBvciBpbnZhbGlkIG51bWJlcnMgZ3JhY2VmdWxseVxuICAgICAgaWYgKHZhbHVlID09PSAnJyB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHZhbCA9IDA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBwYXJzZUZsb2F0KHZhbHVlKTtcbiAgICAgICAgdmFsID0gaXNOYU4ocGFyc2VkKSA/IDAgOiBwYXJzZWQ7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhbCA9IHZhbHVlO1xuICAgIH1cblxuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IFtuYW1lXTogdmFsIH0gfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0Q2hhbmdlID0gKG5hbWU6IHN0cmluZywgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IFtuYW1lXTogdmFsdWUgfSB9KTtcbiAgICBpZiAobmFtZSA9PT0gJ2NyeXB0bzEnKSB7XG4gICAgICAvLyBSZXNldCBjcnlwdG8yIGlmIG5ldyBjcnlwdG8xIGRvZXNuJ3Qgc3VwcG9ydCBjdXJyZW50IGNyeXB0bzJcbiAgICAgIGNvbnN0IHZhbGlkUXVvdGVzID0gQVZBSUxBQkxFX1FVT1RFU19TSU1QTEVbdmFsdWUgYXMga2V5b2YgdHlwZW9mIEFWQUlMQUJMRV9RVU9URVNfU0lNUExFXSB8fCBERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMgfHwgW1wiVVNEVFwiLCBcIlVTRENcIiwgXCJCVENcIl07XG4gICAgICBpZiAoIWNvbmZpZy5jcnlwdG8yIHx8ICFBcnJheS5pc0FycmF5KHZhbGlkUXVvdGVzKSB8fCAhdmFsaWRRdW90ZXMuaW5jbHVkZXMoY29uZmlnLmNyeXB0bzIpKSB7XG4gICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IGNyeXB0bzI6IHZhbGlkUXVvdGVzWzBdIHx8ICdVU0RUJyB9IH0pOyAvLyBFbnN1cmUgY3J5cHRvMiBnZXRzIGEgdmFsaWQgZGVmYXVsdFxuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDcnlwdG8xU2VsZWN0aW9uID0gKGNyeXB0bzogc3RyaW5nKSA9PiB7XG4gICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMTogY3J5cHRvIH0gfSk7XG4gICAgLy8gUmVzZXQgY3J5cHRvMiBiYXNlZCBvbiB0cmFkaW5nIG1vZGUgd2hlbiBjcnlwdG8xIGNoYW5nZXNcbiAgICBpZiAoY29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIpIHtcbiAgICAgIC8vIEluIHN0YWJsZWNvaW4gc3dhcCBtb2RlLCBjcnlwdG8yIHNob3VsZCBiZSBmcm9tIEFMTE9XRURfQ1JZUFRPMSBhbmQgZGlmZmVyZW50IGZyb20gY3J5cHRvMVxuICAgICAgY29uc3QgYWxsb3dlZENyeXB0bzIgPSAoQUxMT1dFRF9DUllQVE8xIHx8IFtcIkJUQ1wiLCBcIkVUSFwiLCBcIkJOQlwiLCBcIlNPTFwiLCBcIkxJTktcIiwgXCJBVkFYXCIsIFwiRE9UXCIsIFwiVU5JXCIsIFwiTkVBUlwiLCBcIkFBVkVcIiwgXCJBVE9NXCIsIFwiVkVUXCIsIFwiUkVOREVSXCIsIFwiUE9MXCIsIFwiQUxHT1wiLCBcIkFSQlwiLCBcIkZFVFwiLCBcIlBBWEdcIiwgXCJHQUxBXCIsIFwiQ1JWXCIsIFwiQ09NUFwiLCBcIkVOSlwiXSkuZmlsdGVyKGMgPT4gYyAhPT0gY3J5cHRvKTtcbiAgICAgIGlmICghYWxsb3dlZENyeXB0bzIuaW5jbHVkZXMoY29uZmlnLmNyeXB0bzIpIHx8IGNvbmZpZy5jcnlwdG8yID09PSBjcnlwdG8pIHtcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMjogYWxsb3dlZENyeXB0bzJbMF0gfSB9KTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gSW4gc2ltcGxlIHNwb3QgbW9kZSwgY3J5cHRvMiBzaG91bGQgYmUgZnJvbSBBTExPV0VEX0NSWVBUTzJcbiAgICAgIGNvbnN0IGFsbG93ZWRDcnlwdG8yID0gQUxMT1dFRF9DUllQVE8yIHx8IFtcIlVTRENcIiwgXCJEQUlcIiwgXCJUVVNEXCIsIFwiRkRVU0RcIiwgXCJVU0RUXCIsIFwiRVVSXCJdO1xuICAgICAgaWYgKCFhbGxvd2VkQ3J5cHRvMi5pbmNsdWRlcyhjb25maWcuY3J5cHRvMikpIHtcbiAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMjogYWxsb3dlZENyeXB0bzJbMF0gfSB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3J5cHRvMlNlbGVjdGlvbiA9IChjcnlwdG86IHN0cmluZykgPT4ge1xuICAgIC8vIEluIHN0YWJsZWNvaW4gc3dhcCBtb2RlLCBlbnN1cmUgY3J5cHRvMiBpcyBkaWZmZXJlbnQgZnJvbSBjcnlwdG8xXG4gICAgaWYgKGNvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiICYmIGNyeXB0byA9PT0gY29uZmlnLmNyeXB0bzEpIHtcbiAgICAgIC8vIERvbid0IGFsbG93IHNlbGVjdGluZyB0aGUgc2FtZSBjcnlwdG8gYXMgY3J5cHRvMSBpbiBzd2FwIG1vZGVcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgY3J5cHRvMjogY3J5cHRvIH0gfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVHJhZGluZ01vZGVDaGFuZ2UgPSAoY2hlY2tlZDogYm9vbGVhbikgPT4ge1xuICAgIGNvbnN0IG5ld01vZGU6IFRyYWRpbmdNb2RlID0gY2hlY2tlZCA/IFwiU3RhYmxlY29pblN3YXBcIiA6IFwiU2ltcGxlU3BvdFwiO1xuXG4gICAgLy8gUmVzZXQgY3J5cHRvMiBiYXNlZCBvbiB0aGUgbmV3IHRyYWRpbmcgbW9kZVxuICAgIGxldCBuZXdDcnlwdG8yOiBzdHJpbmc7XG4gICAgaWYgKG5ld01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIikge1xuICAgICAgLy8gSW4gc3RhYmxlY29pbiBzd2FwIG1vZGUsIGNyeXB0bzIgc2hvdWxkIGJlIGZyb20gQUxMT1dFRF9DUllQVE8xIGFuZCBkaWZmZXJlbnQgZnJvbSBjcnlwdG8xXG4gICAgICBjb25zdCBhbGxvd2VkQ3J5cHRvMiA9IChBTExPV0VEX0NSWVBUTzEgfHwgW1wiQlRDXCIsIFwiRVRIXCIsIFwiQk5CXCIsIFwiU09MXCIsIFwiTElOS1wiLCBcIkFWQVhcIiwgXCJET1RcIiwgXCJVTklcIiwgXCJORUFSXCIsIFwiQUFWRVwiLCBcIkFUT01cIiwgXCJWRVRcIiwgXCJSRU5ERVJcIiwgXCJQT0xcIiwgXCJBTEdPXCIsIFwiQVJCXCIsIFwiRkVUXCIsIFwiUEFYR1wiLCBcIkdBTEFcIiwgXCJDUlZcIiwgXCJDT01QXCIsIFwiRU5KXCJdKS5maWx0ZXIoYyA9PiBjICE9PSBjb25maWcuY3J5cHRvMSk7XG4gICAgICBuZXdDcnlwdG8yID0gYWxsb3dlZENyeXB0bzJbMF07XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEluIHNpbXBsZSBzcG90IG1vZGUsIGNyeXB0bzIgc2hvdWxkIGJlIGZyb20gQUxMT1dFRF9DUllQVE8yXG4gICAgICBjb25zdCBhbGxvd2VkQ3J5cHRvMiA9IEFMTE9XRURfQ1JZUFRPMiB8fCBbXCJVU0RDXCIsIFwiREFJXCIsIFwiVFVTRFwiLCBcIkZEVVNEXCIsIFwiVVNEVFwiLCBcIkVVUlwiXTtcbiAgICAgIG5ld0NyeXB0bzIgPSBhbGxvd2VkQ3J5cHRvMlswXTtcbiAgICB9XG5cbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQ09ORklHJywgcGF5bG9hZDogeyB0cmFkaW5nTW9kZTogbmV3TW9kZSwgY3J5cHRvMjogbmV3Q3J5cHRvMiB9IH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUluY29tZVNwbGl0Q2hhbmdlID0gKGNyeXB0b0tleTogJ2luY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQnIHwgJ2luY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQnLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgbGV0IHBlcmNlbnQgPSBwYXJzZUZsb2F0KHZhbHVlKTtcbiAgICBpZiAoaXNOYU4ocGVyY2VudCkpIHBlcmNlbnQgPSAwO1xuICAgIGlmIChwZXJjZW50IDwgMCkgcGVyY2VudCA9IDA7XG4gICAgaWYgKHBlcmNlbnQgPiAxMDApIHBlcmNlbnQgPSAxMDA7XG5cbiAgICBpZiAoY3J5cHRvS2V5ID09PSAnaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudCcpIHtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9DT05GSUcnLCBwYXlsb2FkOiB7IGluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQ6IHBlcmNlbnQsIGluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQ6IDEwMCAtIHBlcmNlbnQgfSB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU0VUX0NPTkZJRycsIHBheWxvYWQ6IHsgaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudDogcGVyY2VudCwgaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudDogMTAwIC0gcGVyY2VudCB9IH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBSVN1Z2dlc3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFyaXNrVG9sZXJhbmNlIHx8ICFwcmVmZXJyZWRDcnlwdG9zIHx8ICFpbnZlc3RtZW50R29hbHMpIHtcbiAgICAgIHRvYXN0KHsgdGl0bGU6IFwiQUkgU3VnZ2VzdGlvbiBFcnJvclwiLCBkZXNjcmlwdGlvbjogXCJQbGVhc2UgZmlsbCBhbGwgQUkgc3VnZ2VzdGlvbiBmaWVsZHMuXCIsIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIiB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgYXdhaXQgZ2V0VHJhZGluZ01vZGVTdWdnZXN0aW9uKHsgcmlza1RvbGVyYW5jZSwgcHJlZmVycmVkQ3J5cHRvY3VycmVuY2llczogcHJlZmVycmVkQ3J5cHRvcywgaW52ZXN0bWVudEdvYWxzIH0pO1xuICB9O1xuXG5cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhaVN1Z2dlc3Rpb24pIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiQUkgU3VnZ2VzdGlvbiBSZWNlaXZlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8cD48c3Ryb25nPk1vZGU6PC9zdHJvbmc+IHthaVN1Z2dlc3Rpb24uc3VnZ2VzdGVkTW9kZX08L3A+XG4gICAgICAgICAgICA8cD48c3Ryb25nPlJlYXNvbjo8L3N0cm9uZz4ge2FpU3VnZ2VzdGlvbi5yZWFzb259PC9wPlxuICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJtdC0yIGJ0bi1uZW9cIiBvbkNsaWNrPXsoKSA9PiBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQ09ORklHJywgcGF5bG9hZDogeyB0cmFkaW5nTW9kZTogYWlTdWdnZXN0aW9uLnN1Z2dlc3RlZE1vZGUgPT09ICdTaW1wbGUgU3BvdCcgPyAnU2ltcGxlU3BvdCcgOiAnU3RhYmxlY29pblN3YXAnIH19KX0+XG4gICAgICAgICAgICAgIEFwcGx5IFN1Z2dlc3Rpb25cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApLFxuICAgICAgICBkdXJhdGlvbjogSW5maW5pdHksIC8vIFBlcnNpc3QgdW50aWwgbWFudWFsbHkgY2xvc2VkXG4gICAgICB9KTtcbiAgICB9XG4gICAgaWYgKGFpRXJyb3IpIHtcbiAgICAgIHRvYXN0KHsgdGl0bGU6IFwiQUkgU3VnZ2VzdGlvbiBFcnJvclwiLCBkZXNjcmlwdGlvbjogYWlFcnJvciwgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLCBkdXJhdGlvbjogSW5maW5pdHkgfSk7XG4gICAgfVxuICB9LCBbYWlTdWdnZXN0aW9uLCBhaUVycm9yLCB0b2FzdCwgZGlzcGF0Y2hdKTtcblxuXG4gIC8vIFVzZSBzdGF0aWMgY3J5cHRvIGxpc3RzXG4gIGNvbnN0IGNyeXB0bzFPcHRpb25zID0gQVZBSUxBQkxFX0NSWVBUT1MgfHwgW107XG4gIGNvbnN0IGNyeXB0bzJPcHRpb25zID0gY29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlNpbXBsZVNwb3RcIlxuICAgID8gKEFWQUlMQUJMRV9RVU9URVNfU0lNUExFW2NvbmZpZy5jcnlwdG8xIGFzIGtleW9mIHR5cGVvZiBBVkFJTEFCTEVfUVVPVEVTX1NJTVBMRV0gfHwgREVGQVVMVF9RVU9URV9DVVJSRU5DSUVTIHx8IFtcIlVTRFRcIiwgXCJVU0RDXCIsIFwiQlRDXCJdKVxuICAgIDogKEFWQUlMQUJMRV9DUllQVE9TIHx8IFtdKS5maWx0ZXIoYyA9PiBjICE9PSBjb25maWcuY3J5cHRvMSk7IC8vIEVuc3VyZSBDcnlwdG8yIGlzIG5vdCBzYW1lIGFzIENyeXB0bzEgaW4gU3dhcCBtb2RlXG5cbiAgLy8gRGVidWcgbG9nIHRvIHNlZSB3aGF0J3MgYWN0dWFsbHkgbG9hZGVkXG4gIGNvbnNvbGUubG9nKCfwn5SNIERFQlVHOiBBVkFJTEFCTEVfQ1JZUFRPUyBsZW5ndGg6JywgQVZBSUxBQkxFX0NSWVBUT1M/Lmxlbmd0aCk7XG4gIGNvbnNvbGUubG9nKCfwn5SNIERFQlVHOiBjcnlwdG8xT3B0aW9ucyBsZW5ndGg6JywgY3J5cHRvMU9wdGlvbnMubGVuZ3RoKTtcbiAgY29uc29sZS5sb2coJ/CflI0gREVCVUc6IEZpcnN0IDIwIGNyeXB0b3M6JywgQVZBSUxBQkxFX0NSWVBUT1M/LnNsaWNlKDAsIDIwKSk7XG4gIGNvbnNvbGUubG9nKCfwn5SNIERFQlVHOiBBTExPV0VEX0NSWVBUTzE6JywgQUxMT1dFRF9DUllQVE8xKTtcbiAgY29uc29sZS5sb2coJ/CflI0gREVCVUc6IEFMTE9XRURfQ1JZUFRPMjonLCBBTExPV0VEX0NSWVBUTzIpO1xuXG4gIHJldHVybiAoXG4gICAgPGFzaWRlIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTgwIGxnOnctOTYgYmctc2lkZWJhciB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCBwLTQgYm9yZGVyLXItMiBib3JkZXItc2lkZWJhci1ib3JkZXIgZmxleCBmbGV4LWNvbCBoLXNjcmVlblwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtc2lkZWJhci1wcmltYXJ5XCI+VHJhZGluZyBDb25maWd1cmF0aW9uPC9oMj5cbiAgICAgICAgPEJhY2tlbmRTdGF0dXMgY2xhc3NOYW1lPVwidGV4dC14c1wiIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxTY3JvbGxBcmVhIGNsYXNzTmFtZT1cImZsZXgtMSBwci0yXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIFRyYWRpbmcgTW9kZSAqL31cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1zaWRlYmFyLWFjY2VudCBib3JkZXItc2lkZWJhci1ib3JkZXJcIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc2lkZWJhci1hY2NlbnQtZm9yZWdyb3VuZFwiPlRyYWRpbmcgTW9kZTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgICAgaWQ9XCJlbmFibGVTdGFibGVjb2luU3dhcFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17aGFuZGxlVHJhZGluZ01vZGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImVuYWJsZVN0YWJsZWNvaW5Td2FwXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCI+XG4gICAgICAgICAgICAgICAgICBFbmFibGUgU3RhYmxlY29pbiBTd2FwIE1vZGVcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBDdXJyZW50IG1vZGU6IHtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU2ltcGxlU3BvdFwiID8gXCJTaW1wbGUgU3BvdFwiIDogXCJTdGFibGVjb2luIFN3YXBcIn1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7Y29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIgJiYgKFxuICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwcmVmZXJyZWRTdGFibGVjb2luXCI+UHJlZmVycmVkIFN0YWJsZWNvaW48L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdCBuYW1lPVwicHJlZmVycmVkU3RhYmxlY29pblwiIHZhbHVlPXtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn0gb25WYWx1ZUNoYW5nZT17KHZhbCkgPT4gaGFuZGxlU2VsZWN0Q2hhbmdlKFwicHJlZmVycmVkU3RhYmxlY29pblwiLCB2YWwgYXMgc3RyaW5nKX0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGlkPVwicHJlZmVycmVkU3RhYmxlY29pblwiPjxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCBzdGFibGVjb2luXCIgLz48L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50IGNsYXNzTmFtZT1cIm1heC1oLVszMDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAge0FWQUlMQUJMRV9TVEFCTEVDT0lOUyAmJiBBVkFJTEFCTEVfU1RBQkxFQ09JTlMubWFwKHNjID0+IDxTZWxlY3RJdGVtIGtleT17c2N9IHZhbHVlPXtzY30+e3NjfTwvU2VsZWN0SXRlbT4pfVxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIEFJIFRyYWRpbmcgTW9kZSBTdWdnZXN0aW9uICovfVxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXNpZGViYXItYWNjZW50IGJvcmRlci1zaWRlYmFyLWJvcmRlclwiPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kIGZsZXggaXRlbXMtY2VudGVyXCI+PExpZ2h0YnVsYiBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz4gQUkgTW9kZSBTdWdnZXN0aW9uPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJyaXNrVG9sZXJhbmNlXCI+UmlzayBUb2xlcmFuY2U8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3Jpc2tUb2xlcmFuY2V9IG9uVmFsdWVDaGFuZ2U9e3NldFJpc2tUb2xlcmFuY2V9PlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgaWQ9XCJyaXNrVG9sZXJhbmNlXCI+PFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHJpc2sgdG9sZXJhbmNlXCIgLz48L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJsb3dcIj5Mb3c8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwibWVkaXVtXCI+TWVkaXVtPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImhpZ2hcIj5IaWdoPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInByZWZlcnJlZENyeXB0b3NcIj5QcmVmZXJyZWQgQ3J5cHRvY3VycmVuY2llcyAoY29tbWEtc2VwYXJhdGVkKTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPElucHV0IGlkPVwicHJlZmVycmVkQ3J5cHRvc1wiIHZhbHVlPXtwcmVmZXJyZWRDcnlwdG9zfSBvbkNoYW5nZT17KGUpID0+IHNldFByZWZlcnJlZENyeXB0b3MoZS50YXJnZXQudmFsdWUpfSBwbGFjZWhvbGRlcj1cImUuZy4sIEJUQywgRVRIXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbnZlc3RtZW50R29hbHNcIj5JbnZlc3RtZW50IEdvYWxzPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXQgaWQ9XCJpbnZlc3RtZW50R29hbHNcIiB2YWx1ZT17aW52ZXN0bWVudEdvYWxzfSBvbkNoYW5nZT17KGUpID0+IHNldEludmVzdG1lbnRHb2FscyhlLnRhcmdldC52YWx1ZSl9IHBsYWNlaG9sZGVyPVwiZS5nLiwgTG9uZyB0ZXJtLCBTaG9ydCB0ZXJtIHByb2ZpdFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUFJU3VnZ2VzdGlvbn0gZGlzYWJsZWQ9e2FpTG9hZGluZ30gY2xhc3NOYW1lPVwidy1mdWxsIGJ0bi1uZW9cIj5cbiAgICAgICAgICAgICAgICB7YWlMb2FkaW5nICYmIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPn1cbiAgICAgICAgICAgICAgICBHZXQgQUkgU3VnZ2VzdGlvblxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIFRyYWRpbmcgUGFpciAqL31cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1zaWRlYmFyLWFjY2VudCBib3JkZXItc2lkZWJhci1ib3JkZXJcIj5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPjxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zaWRlYmFyLWFjY2VudC1mb3JlZ3JvdW5kXCI+VHJhZGluZyBQYWlyPC9DYXJkVGl0bGU+PC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8Q3J5cHRvSW5wdXRcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkNyeXB0byAxIChCYXNlKVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5jcnlwdG8xfVxuICAgICAgICAgICAgICAgIGFsbG93ZWRDcnlwdG9zPXtBTExPV0VEX0NSWVBUTzEgfHwgW1wiQlRDXCIsIFwiRVRIXCIsIFwiQk5CXCIsIFwiU09MXCIsIFwiTElOS1wiLCBcIkFWQVhcIiwgXCJET1RcIiwgXCJVTklcIiwgXCJORUFSXCIsIFwiQUFWRVwiLCBcIkFUT01cIiwgXCJWRVRcIiwgXCJSRU5ERVJcIiwgXCJQT0xcIiwgXCJBTEdPXCIsIFwiQVJCXCIsIFwiRkVUXCIsIFwiUEFYR1wiLCBcIkdBTEFcIiwgXCJDUlZcIiwgXCJDT01QXCIsIFwiRU5KXCJdfVxuICAgICAgICAgICAgICAgIG9uVmFsaWRDcnlwdG89e2hhbmRsZUNyeXB0bzFTZWxlY3Rpb259XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBCVEMsIEVUSCwgU09MXCJcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIkVudGVyIHRoZSBiYXNlIGNyeXB0b2N1cnJlbmN5IHN5bWJvbFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxDcnlwdG9JbnB1dFxuICAgICAgICAgICAgICAgIGxhYmVsPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIiA/IFwiQ3J5cHRvIDJcIiA6IFwiQ3J5cHRvIDIgKFN0YWJsZWNvaW4pXCJ9XG4gICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5jcnlwdG8yfVxuICAgICAgICAgICAgICAgIGFsbG93ZWRDcnlwdG9zPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIlxuICAgICAgICAgICAgICAgICAgPyAoQUxMT1dFRF9DUllQVE8xIHx8IFtcIkJUQ1wiLCBcIkVUSFwiLCBcIkJOQlwiLCBcIlNPTFwiLCBcIkxJTktcIiwgXCJBVkFYXCIsIFwiRE9UXCIsIFwiVU5JXCIsIFwiTkVBUlwiLCBcIkFBVkVcIiwgXCJBVE9NXCIsIFwiVkVUXCIsIFwiUkVOREVSXCIsIFwiUE9MXCIsIFwiQUxHT1wiLCBcIkFSQlwiLCBcIkZFVFwiLCBcIlBBWEdcIiwgXCJHQUxBXCIsIFwiQ1JWXCIsIFwiQ09NUFwiLCBcIkVOSlwiXSkuZmlsdGVyKGMgPT4gYyAhPT0gY29uZmlnLmNyeXB0bzEpXG4gICAgICAgICAgICAgICAgICA6IChBTExPV0VEX0NSWVBUTzIgfHwgW1wiVVNEQ1wiLCBcIkRBSVwiLCBcIlRVU0RcIiwgXCJGRFVTRFwiLCBcIlVTRFRcIiwgXCJFVVJcIl0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG9uVmFsaWRDcnlwdG89e2hhbmRsZUNyeXB0bzJTZWxlY3Rpb259XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2NvbmZpZy50cmFkaW5nTW9kZSA9PT0gXCJTdGFibGVjb2luU3dhcFwiID8gXCJlLmcuLCBCVEMsIEVUSCwgU09MXCIgOiBcImUuZy4sIFVTRFQsIFVTREMsIERBSVwifVxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtjb25maWcudHJhZGluZ01vZGUgPT09IFwiU3RhYmxlY29pblN3YXBcIiA/IFwiRW50ZXIgdGhlIHNlY29uZCBjcnlwdG9jdXJyZW5jeSBzeW1ib2xcIiA6IFwiRW50ZXIgdGhlIHF1b3RlL3N0YWJsZWNvaW4gc3ltYm9sXCJ9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBQYXJhbWV0ZXJzICovfVxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLXNpZGViYXItYWNjZW50IGJvcmRlci1zaWRlYmFyLWJvcmRlclwiPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+PENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNpZGViYXItYWNjZW50LWZvcmVncm91bmRcIj5QYXJhbWV0ZXJzPC9DYXJkVGl0bGU+PC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgIHsgbmFtZTogXCJiYXNlQmlkXCIsIGxhYmVsOiBcIkJhc2UgQmlkIChDcnlwdG8gMilcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIwLjAxXCIgfSxcbiAgICAgICAgICAgICAgICB7IG5hbWU6IFwibXVsdGlwbGllclwiLCBsYWJlbDogXCJNdWx0aXBsaWVyXCIsIHR5cGU6IFwibnVtYmVyXCIsIHN0ZXA6IFwiMC4wMDFcIiB9LFxuICAgICAgICAgICAgICAgIHsgbmFtZTogXCJudW1EaWdpdHNcIiwgbGFiZWw6IFwiRGlzcGxheSBEaWdpdHNcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIxXCIgfSxcbiAgICAgICAgICAgICAgICB7IG5hbWU6IFwic2xpcHBhZ2VQZXJjZW50XCIsIGxhYmVsOiBcIlNsaXBwYWdlICVcIiwgdHlwZTogXCJudW1iZXJcIiwgc3RlcDogXCIwLjAxXCIgfSxcbiAgICAgICAgICAgICAgXS5tYXAoZmllbGQgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZC5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPXtmaWVsZC5uYW1lfT57ZmllbGQubGFiZWx9PC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBpZD17ZmllbGQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgbmFtZT17ZmllbGQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgdHlwZT17ZmllbGQudHlwZX1cbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZ1tmaWVsZC5uYW1lIGFzIGtleW9mIHR5cGVvZiBjb25maWddIGFzIHN0cmluZyB8IG51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBzdGVwPXtmaWVsZC5zdGVwfVxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxMYWJlbD5Db3VwbGUgSW5jb21lICUgU3BsaXQgKG11c3Qgc3VtIHRvIDEwMCk8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPntjb25maWcuY3J5cHRvMX0lPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPElucHV0IGlkPVwiaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudFwiIHR5cGU9XCJudW1iZXJcIiB2YWx1ZT17Y29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnR9IG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5jb21lU3BsaXRDaGFuZ2UoJ2luY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQnLCBlLnRhcmdldC52YWx1ZSl9IG1pbj1cIjBcIiBtYXg9XCIxMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnRcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+e2NvbmZpZy5jcnlwdG8yfSU8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8SW5wdXQgaWQ9XCJpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50XCIgdHlwZT1cIm51bWJlclwiIHZhbHVlPXtjb25maWcuaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudH0gb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbmNvbWVTcGxpdENoYW5nZSgnaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCcsIGUudGFyZ2V0LnZhbHVlKX0gbWluPVwiMFwiIG1heD1cIjEwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L1Njcm9sbEFyZWE+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBtdC00XCI+XG4gICAgICAgIDxTZXBhcmF0b3IgY2xhc3NOYW1lPVwibWItNCBiZy1zaWRlYmFyLWJvcmRlclwiIC8+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgIFwidGV4dC1jZW50ZXIgdGV4dC1zbSBmb250LW1lZGl1bSBwLTIgcm91bmRlZC1tZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiBib3JkZXItMiBib3JkZXItYm9yZGVyXCIsXG4gICAgICAgICAgICAgIGlzQm90QWN0aXZlID8gXCJiZy1ncmVlbi02MDAgdGV4dC1wcmltYXJ5LWZvcmVncm91bmRcIiA6IFwiYmctbXV0ZWQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCJcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzQm90QWN0aXZlID8gPFBvd2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIi8+IDogKGlzQm90V2FybWluZ1VwID8gPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+IDogPFBvd2VyT2ZmIGNsYXNzTmFtZT1cImgtNCB3LTRcIi8+KX1cblxuICAgICAgICAgICAgQm90IFN0YXR1czoge2lzQm90QWN0aXZlID8gJ1J1bm5pbmcnIDogKGlzQm90V2FybWluZ1VwID8gJ1dhcm1pbmcgVXAnIDogJ1N0b3BwZWQnKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHNldElzVGFyZ2V0TW9kYWxPcGVuKHRydWUpfSBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLW91dGxpbmUtbmVvXCI+U2V0IFRhcmdldCBQcmljZXM8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHNldElzQWxhcm1Nb2RhbE9wZW4odHJ1ZSl9IGNsYXNzTmFtZT1cInctZnVsbCBidG4tb3V0bGluZS1uZW9cIj5TZXQgQWxhcm08L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChpc0JvdEFjdGl2ZSkge1xuICAgICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NZU1RFTV9TVE9QX0JPVCcgfSk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnU1lTVEVNX1NUQVJUX0JPVF9JTklUSUFURScgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9e2NuKCd3LWZ1bGwgYnRuLW5lbycsIGlzQm90QWN0aXZlIHx8IGlzQm90V2FybWluZ1VwID8gJ2JnLWRlc3RydWN0aXZlIGhvdmVyOmJnLWRlc3RydWN0aXZlLzkwJyA6ICdiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNjAwLzkwJyl9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNCb3RXYXJtaW5nVXB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzQm90QWN0aXZlID8gPFBvd2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIi8+IDogKGlzQm90V2FybWluZ1VwID8gPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+IDogPFBvd2VyT2ZmIGNsYXNzTmFtZT1cImgtNCB3LTRcIi8+KX1cbiAgICAgICAgICAgIHtpc0JvdEFjdGl2ZSA/ICdTdG9wIEJvdCcgOiAoaXNCb3RXYXJtaW5nVXAgPyAnV2FybWluZyBVcC4uLicgOiAnU3RhcnQgQm90Jyl9XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTWVNURU1fUkVTRVRfQk9UJyB9KTtcbiAgICAgICAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgICAgICAgIHRpdGxlOiBcIkJvdCBSZXNldFwiLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIlRyYWRpbmcgYm90IGhhcyBiZWVuIHJlc2V0IHRvIGZyZXNoIHN0YXRlLiBBbGwgcG9zaXRpb25zIGNsZWFyZWQuXCIsXG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDMwMDBcbiAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJ0bi1vdXRsaW5lLW5lb1wiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNCb3RXYXJtaW5nVXB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFJvdGF0ZUNjdyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIi8+XG4gICAgICAgICAgICBSZXNldCBCb3RcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPFRhcmdldFByaWNlTW9kYWwgaXNPcGVuPXtpc1RhcmdldE1vZGFsT3Blbn0gb25DbG9zZT17KCkgPT4gc2V0SXNUYXJnZXRNb2RhbE9wZW4oZmFsc2UpfSBvblNldFRhcmdldFByaWNlcz17Y29udGV4dFNldFRhcmdldFByaWNlc30gLz5cbiAgICAgIDxBbGFybUNvbmZpZ01vZGFsIGlzT3Blbj17aXNBbGFybU1vZGFsT3Blbn0gb25DbG9zZT17KCkgPT4gc2V0SXNBbGFybU1vZGFsT3BlbihmYWxzZSl9IC8+XG4gICAgPC9hc2lkZT5cbiAgKTtcbn1cblxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VUcmFkaW5nQ29udGV4dCIsInVzZUFJQ29udGV4dCIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJDaGVja2JveCIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlNjcm9sbEFyZWEiLCJTZXBhcmF0b3IiLCJUYXJnZXRQcmljZU1vZGFsIiwiQWxhcm1Db25maWdNb2RhbCIsIkFWQUlMQUJMRV9DUllQVE9TIiwiQVZBSUxBQkxFX1FVT1RFU19TSU1QTEUiLCJBVkFJTEFCTEVfU1RBQkxFQ09JTlMiLCJBTExPV0VEX0NSWVBUTzEiLCJBTExPV0VEX0NSWVBUTzIiLCJDcnlwdG9JbnB1dCIsIkJhY2tlbmRTdGF0dXMiLCJERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMiLCJMb2FkZXIyIiwiTGlnaHRidWxiIiwiUG93ZXIiLCJQb3dlck9mZiIsIlJvdGF0ZUNjdyIsInVzZVRvYXN0IiwiY24iLCJUcmFkaW5nQ29uZmlnU2lkZWJhciIsInRyYWRpbmdDb250ZXh0IiwiZXJyb3IiLCJjb25zb2xlIiwiYXNpZGUiLCJjbGFzc05hbWUiLCJkaXYiLCJwIiwiY29uZmlnIiwiZGlzcGF0Y2giLCJib3RTeXN0ZW1TdGF0dXMiLCJhcHBTZXR0aW5ncyIsInNldFRhcmdldFByaWNlcyIsImNvbnRleHRTZXRUYXJnZXRQcmljZXMiLCJpc0JvdEFjdGl2ZSIsImlzQm90V2FybWluZ1VwIiwiYWlDb250ZXh0Iiwid2FybiIsInN1Z2dlc3Rpb24iLCJpc0xvYWRpbmciLCJnZXRUcmFkaW5nTW9kZVN1Z2dlc3Rpb24iLCJQcm9taXNlIiwicmVzb2x2ZSIsImFpU3VnZ2VzdGlvbiIsImFpTG9hZGluZyIsImFpRXJyb3IiLCJ0b2FzdCIsImlzVGFyZ2V0TW9kYWxPcGVuIiwic2V0SXNUYXJnZXRNb2RhbE9wZW4iLCJpc0FsYXJtTW9kYWxPcGVuIiwic2V0SXNBbGFybU1vZGFsT3BlbiIsInJpc2tUb2xlcmFuY2UiLCJzZXRSaXNrVG9sZXJhbmNlIiwicHJlZmVycmVkQ3J5cHRvcyIsInNldFByZWZlcnJlZENyeXB0b3MiLCJpbnZlc3RtZW50R29hbHMiLCJzZXRJbnZlc3RtZW50R29hbHMiLCJoYW5kbGVJbnB1dENoYW5nZSIsImUiLCJuYW1lIiwidmFsdWUiLCJ0eXBlIiwiY2hlY2tlZCIsInRhcmdldCIsInZhbCIsInVuZGVmaW5lZCIsInBhcnNlZCIsInBhcnNlRmxvYXQiLCJpc05hTiIsInBheWxvYWQiLCJoYW5kbGVTZWxlY3RDaGFuZ2UiLCJ2YWxpZFF1b3RlcyIsImNyeXB0bzIiLCJBcnJheSIsImlzQXJyYXkiLCJpbmNsdWRlcyIsImhhbmRsZUNyeXB0bzFTZWxlY3Rpb24iLCJjcnlwdG8iLCJjcnlwdG8xIiwidHJhZGluZ01vZGUiLCJhbGxvd2VkQ3J5cHRvMiIsImZpbHRlciIsImMiLCJoYW5kbGVDcnlwdG8yU2VsZWN0aW9uIiwiaGFuZGxlVHJhZGluZ01vZGVDaGFuZ2UiLCJuZXdNb2RlIiwibmV3Q3J5cHRvMiIsImhhbmRsZUluY29tZVNwbGl0Q2hhbmdlIiwiY3J5cHRvS2V5IiwicGVyY2VudCIsImluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQiLCJpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50IiwiaGFuZGxlQUlTdWdnZXN0aW9uIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInZhcmlhbnQiLCJwcmVmZXJyZWRDcnlwdG9jdXJyZW5jaWVzIiwic3Ryb25nIiwic3VnZ2VzdGVkTW9kZSIsInJlYXNvbiIsInNpemUiLCJvbkNsaWNrIiwiZHVyYXRpb24iLCJJbmZpbml0eSIsImNyeXB0bzFPcHRpb25zIiwiY3J5cHRvMk9wdGlvbnMiLCJsb2ciLCJsZW5ndGgiLCJzbGljZSIsImgyIiwiaWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJodG1sRm9yIiwicHJlZmVycmVkU3RhYmxlY29pbiIsIm9uVmFsdWVDaGFuZ2UiLCJwbGFjZWhvbGRlciIsIm1hcCIsInNjIiwib25DaGFuZ2UiLCJkaXNhYmxlZCIsImxhYmVsIiwiYWxsb3dlZENyeXB0b3MiLCJvblZhbGlkQ3J5cHRvIiwic3RlcCIsImZpZWxkIiwibWluIiwibWF4IiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU2V0VGFyZ2V0UHJpY2VzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});