"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/AppHeader.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppHeader.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/Logo */ \"(app-pages-browser)/./src/components/shared/Logo.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Home,LogOut,PlusSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_backend_status__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/backend-status */ \"(app-pages-browser)/./src/components/ui/backend-status.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AppHeader() {\n    _s();\n    const { logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Removed 'dispatch' from useTradingContext as it's no longer needed here for new session\n    // const { dispatch } = useTradingContext();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const handleNewSession = ()=>{\n        // Directly open a new tab/window. The new tab will initialize its own independent TradingContext.\n        window.open('/dashboard', '_blank');\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Home',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 31,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            href: '/admin',\n            label: 'Admin Panel',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 32,\n                columnNumber: 51\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Logo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        useFullName: false\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_backend_status__WEBPACK_IMPORTED_MODULE_7__.NetworkStatus, {}, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center gap-2 sm:gap-3\",\n                children: [\n                    navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: pathname === item.href ? \"default\" : \"ghost\",\n                            size: \"sm\",\n                            asChild: true,\n                            className: \"\".concat(pathname === item.href ? 'btn-neo' : 'hover:bg-accent/50'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, item.label, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleNewSession,\n                        className: \"hover:bg-accent/50 flex items-center gap-2\",\n                        title: \"Open a new independent trading session in a new tab\" // Added title for clarity\n                        ,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"New Session\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 10\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: handleLogout,\n                        className: \"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Home_LogOut_PlusSquare_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\AppHeader.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(AppHeader, \"gxli3sVmWq4I33HjyK3OPLJOU50=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = AppHeader;\nvar _c;\n$RefreshReg$(_c, \"AppHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppHeader.tsx\n"));

/***/ })

});