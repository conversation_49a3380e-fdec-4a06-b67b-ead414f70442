"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/OrdersTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction OrdersTable() {\n    _s();\n    const { getDisplayOrders, config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const displayOrders = getDisplayOrders();\n    const formatNumber = function(num) {\n        let forceSign = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        const fixedNum = num.toFixed(config.numDigits);\n        if (forceSign && num > 0) return \"+\".concat(fixedNum);\n        return fixedNum;\n    };\n    const formatPercent = (num)=>{\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        return \"\".concat(num.toFixed(2), \"%\");\n    };\n    const columns = [\n        {\n            key: \"#\",\n            label: \"#\"\n        },\n        {\n            key: \"status\",\n            label: \"Status\"\n        },\n        {\n            key: \"orderLevel\",\n            label: \"Level\"\n        },\n        {\n            key: \"valueLevel\",\n            label: \"Value\"\n        },\n        {\n            key: \"crypto2Var\",\n            label: \"\".concat(config.crypto2 || \"Crypto 2\", \" Var.\")\n        },\n        {\n            key: \"crypto1Var\",\n            label: \"\".concat(config.crypto1 || \"Crypto 1\", \" Var.\")\n        },\n        {\n            key: \"targetPrice\",\n            label: \"Target Price\"\n        },\n        {\n            key: \"percentFromActualPrice\",\n            label: \"% from Actual\"\n        },\n        {\n            key: \"incomeCrypto1\",\n            label: \"Income \".concat(config.crypto1 || \"Crypto 1\")\n        },\n        {\n            key: \"incomeCrypto2\",\n            label: \"Income \".concat(config.crypto2 || \"Crypto 2\")\n        },\n        {\n            key: \"originalCostCrypto2\",\n            label: \"Original Cost \".concat(config.crypto2 || \"Crypto 2\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n            className: \"w-full whitespace-nowrap\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                className: \"bg-card hover:bg-card\",\n                                children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm\",\n                                        children: col.label\n                                    }, col.key, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                            children: displayOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: columns.length,\n                                    className: \"h-24 text-center text-muted-foreground\",\n                                    children: 'No target prices set. Use \"Set Target Prices\" in the sidebar.'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, this) : displayOrders.map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                    className: \"hover:bg-card/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.counter\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: row.status === \"Full\" ? \"default\" : \"secondary\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(row.status === \"Full\" ? \"bg-green-600 text-white\" : \"bg-yellow-500 text-black\", \"font-bold\"),\n                                                children: row.status\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.orderLevel\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.valueLevel)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto2Var && row.crypto2Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto2Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto1Var && row.crypto1Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto1Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs font-semibold text-primary\",\n                                            children: formatNumber(row.targetPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.percentFromActualPrice < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatPercent(row.percentFromActualPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto1 && row.incomeCrypto1 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.incomeCrypto1)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 20\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.incomeCrypto2 && row.incomeCrypto2 < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.incomeCrypto2)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.originalCostCrypto2)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollBar, {\n                    orientation: \"horizontal\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersTable, \"UL7ACZxyGIzSlHTRji6CgpbrwIo=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = OrdersTable;\nvar _c;\n$RefreshReg$(_c, \"OrdersTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\n"));

/***/ })

});