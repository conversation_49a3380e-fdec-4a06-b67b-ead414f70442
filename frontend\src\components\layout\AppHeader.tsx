
"use client";

import React from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import Logo from '@/components/shared/Logo';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Home, PlusSquare, Settings, LogOut, Briefcase } from 'lucide-react';
import { NetworkStatus } from '@/components/ui/backend-status';

export default function AppHeader() {
  const { logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const handleLogout = () => {
    logout();
  };

  const handleNewSession = () => {
    // Directly open a new tab/window. The new tab will initialize its own independent TradingContext.
    window.open('/dashboard', '_blank');
  };



  const navItems = [
    { href: '/dashboard', label: 'Home', icon: <Home /> },
    { href: '/admin', label: 'Admin Panel', icon: <Briefcase /> },
  ];

  return (
    <header className="sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border">
      <div className="flex items-center gap-4">
        <Logo useFullName={false} />
        <NetworkStatus />
      </div>
      <nav className="flex items-center gap-2 sm:gap-3">
        {navItems.map((item) => (
          <Button
            key={item.label}
            variant={pathname === item.href ? "default" : "ghost"}
            size="sm"
            asChild
            className={`${pathname === item.href ? 'btn-neo' : 'hover:bg-accent/50'}`}
          >
            <Link href={item.href} className="flex items-center gap-2">
              {item.icon}
              <span className="hidden sm:inline">{item.label}</span>
            </Link>
          </Button>
        ))}
         <Button
            variant="ghost"
            size="sm"
            onClick={handleNewSession}
            className="hover:bg-accent/50 flex items-center gap-2"
            title="Open a new independent trading session in a new tab"
          >
            <PlusSquare />
            <span className="hidden sm:inline">New Session</span>
          </Button>

        <Button variant="ghost" size="sm" onClick={handleLogout} className="hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2">
          <LogOut />
          <span className="hidden sm:inline">Logout</span>
        </Button>
      </nav>
    </header>
  );
}
