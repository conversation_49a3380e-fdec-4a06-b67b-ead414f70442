"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/analytics/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SessionAwareAnalytics.tsx":
/*!************************************************************!*\
  !*** ./src/components/dashboard/SessionAwareAnalytics.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAwareAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/percent.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Percent,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Helper function to calculate KPIs from order history\nconst calculateKPIs = (orderHistory, config)=>{\n    const sellTrades = orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined);\n    const totalProfitLossCrypto2 = sellTrades.reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0);\n    const totalProfitLossCrypto1 = sellTrades.reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto1 || 0), 0);\n    const profitableTrades = sellTrades.filter((trade)=>(trade.realizedProfitLossCrypto2 || 0) > 0).length;\n    const winRate = sellTrades.length > 0 ? profitableTrades / sellTrades.length * 100 : 0;\n    const totalTradesExecuted = orderHistory.length;\n    const buyTrades = orderHistory.filter((trade)=>trade.orderType === 'BUY').length;\n    const avgProfitPerTradeCrypto2 = sellTrades.length > 0 ? totalProfitLossCrypto2 / sellTrades.length : 0;\n    const avgProfitPerTradeCrypto1 = sellTrades.length > 0 ? totalProfitLossCrypto1 / sellTrades.length : 0;\n    return {\n        totalProfitLossCrypto1: parseFloat(totalProfitLossCrypto1.toFixed(config.numDigits)),\n        totalProfitLossCrypto2: parseFloat(totalProfitLossCrypto2.toFixed(config.numDigits)),\n        winRate: parseFloat(winRate.toFixed(2)),\n        totalTradesExecuted,\n        buyTrades,\n        sellTrades: sellTrades.length,\n        avgProfitPerTradeCrypto2: parseFloat(avgProfitPerTradeCrypto2.toFixed(config.numDigits)),\n        avgProfitPerTradeCrypto1: parseFloat(avgProfitPerTradeCrypto1.toFixed(config.numDigits))\n    };\n};\n// Generate P&L chart data\nconst generatePnlChartData = (orderHistory, crypto2Symbol)=>{\n    const sellTrades = orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined);\n    let cumulativePnL = 0;\n    return sellTrades.map((trade, index)=>{\n        cumulativePnL += trade.realizedProfitLossCrypto2 || 0;\n        return {\n            date: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(new Date(trade.timestamp), 'MMM dd HH:mm'),\n            pnl: parseFloat(cumulativePnL.toFixed(4)),\n            trade: index + 1\n        };\n    });\n};\nfunction SessionAwareAnalytics() {\n    _s();\n    const { orderHistory, config, getDisplayOrders } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__.useTradingContext)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('current');\n    const [selectedSessionHistory, setSelectedSessionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionConfig, setSelectedSessionConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(config);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareAnalytics.useEffect\": ()=>{\n            loadSessions();\n        }\n    }[\"SessionAwareAnalytics.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareAnalytics.useEffect\": ()=>{\n            if (selectedSessionId === 'current') {\n                setSelectedSessionHistory(orderHistory);\n                setSelectedSessionConfig(config);\n            } else {\n                const session = sessionManager.loadSession(selectedSessionId);\n                if (session) {\n                    setSelectedSessionHistory(session.orderHistory);\n                    setSelectedSessionConfig(session.config);\n                }\n            }\n        }\n    }[\"SessionAwareAnalytics.useEffect\"], [\n        selectedSessionId,\n        orderHistory,\n        config\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const kpis = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[kpis]\": ()=>calculateKPIs(selectedSessionHistory, selectedSessionConfig)\n    }[\"SessionAwareAnalytics.useMemo[kpis]\"], [\n        selectedSessionHistory,\n        selectedSessionConfig\n    ]);\n    const pnlChartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[pnlChartData]\": ()=>generatePnlChartData(selectedSessionHistory, selectedSessionConfig.crypto2)\n    }[\"SessionAwareAnalytics.useMemo[pnlChartData]\"], [\n        selectedSessionHistory,\n        selectedSessionConfig.crypto2\n    ]);\n    const currentUnrealizedPL = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[currentUnrealizedPL]\": ()=>{\n            if (selectedSessionId !== 'current') return '0.0000'; // Only show unrealized P/L for current session\n            const currentActiveOrders = getDisplayOrders();\n            const rawValue = currentActiveOrders.reduce({\n                \"SessionAwareAnalytics.useMemo[currentUnrealizedPL].rawValue\": (sum, order)=>{\n                    if (order.status === 'Full' && order.incomeCrypto2 !== undefined) {\n                        return sum + order.incomeCrypto2;\n                    }\n                    return sum;\n                }\n            }[\"SessionAwareAnalytics.useMemo[currentUnrealizedPL].rawValue\"], 0);\n            return rawValue.toFixed(selectedSessionConfig.numDigits);\n        }\n    }[\"SessionAwareAnalytics.useMemo[currentUnrealizedPL]\"], [\n        getDisplayOrders,\n        selectedSessionConfig.numDigits,\n        selectedSessionId\n    ]);\n    const getSelectedSessionInfo = ()=>{\n        if (selectedSessionId === 'current') {\n            const displayPair = config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : \"Crypto 1/Crypto 2\";\n            return {\n                name: 'Current Session',\n                pair: displayPair,\n                isActive: true\n            };\n        }\n        return sessions.find((s)=>s.id === selectedSessionId);\n    };\n    const selectedSession = getSelectedSessionInfo();\n    const kpiCards = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SessionAwareAnalytics.useMemo[kpiCards]\": ()=>[\n                {\n                    title: \"Total Realized P/L (\".concat(selectedSessionConfig.crypto1 || \"Crypto 1\", \")\"),\n                    value: kpis.totalProfitLossCrypto1,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Sum of profits from sell trades in Crypto1\",\n                    isProfit: kpis.totalProfitLossCrypto1 >= 0\n                },\n                {\n                    title: \"Total Realized P/L (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: kpis.totalProfitLossCrypto2,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Sum of profits from sell trades in Crypto2\",\n                    isProfit: kpis.totalProfitLossCrypto2 >= 0\n                },\n                {\n                    title: \"Win Rate\",\n                    value: \"\".concat(kpis.winRate, \"%\"),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Profitable sell trades / Total sell trades\",\n                    isProfit: kpis.winRate >= 50\n                },\n                {\n                    title: \"Total Trades\",\n                    value: kpis.totalTradesExecuted,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this),\n                    description: \"\".concat(kpis.buyTrades, \" buys, \").concat(kpis.sellTrades, \" sells\"),\n                    isProfit: true\n                },\n                {\n                    title: \"Avg Profit/Trade (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: kpis.avgProfitPerTradeCrypto2,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Average profit per sell trade\",\n                    isProfit: kpis.avgProfitPerTradeCrypto2 >= 0\n                },\n                {\n                    title: \"Current Unrealized P/L (\".concat(selectedSessionConfig.crypto2 || \"Crypto 2\", \")\"),\n                    value: currentUnrealizedPL,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-6 w-6 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this),\n                    description: \"Unrealized profit/loss from active positions\",\n                    isProfit: parseFloat(currentUnrealizedPL) >= 0,\n                    isCurrentOnly: true\n                }\n            ]\n    }[\"SessionAwareAnalytics.useMemo[kpiCards]\"], [\n        kpis,\n        selectedSessionConfig,\n        currentUnrealizedPL\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"Session Analytics\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"View trading analytics for current and past sessions.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium mb-2 block\",\n                                            children: \"Select Session:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedSessionId,\n                                            onValueChange: setSelectedSessionId,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[300px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select a session\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: \"current\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"default\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Current\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Current Session (\",\n                                                                            config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : 'Crypto 1/Crypto 2 = 0',\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                                                                    className: \"my-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                        value: session.id,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: session.isActive ? \"default\" : \"secondary\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: session.isActive ? \"Current\" : \"Past\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                                    lineNumber: 195,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                                    lineNumber: 198,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, session.id, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            selectedSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium\",\n                                                    children: selectedSession.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: selectedSession.pair\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedSession.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"default\",\n                                            className: \"text-xs\",\n                                            children: \"Active\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                children: kpiCards.map((kpi, index)=>{\n                    // Hide current-only KPIs for past sessions\n                    if (kpi.isCurrentOnly && selectedSessionId !== 'current') {\n                        return null;\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"border-2 border-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: kpi.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this),\n                                    kpi.icon\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(typeof kpi.value === 'number' ? kpi.isProfit ? 'text-green-600' : 'text-red-600' : 'text-foreground'),\n                                        children: typeof kpi.value === 'number' ? kpi.value.toFixed(selectedSessionConfig.numDigits) : kpi.value\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: kpi.description\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: [\n                                    \"Cumulative Profit/Loss Over Time (\",\n                                    selectedSessionConfig.crypto2,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Chart visualization of trading performance for \",\n                                    (selectedSession === null || selectedSession === void 0 ? void 0 : selectedSession.name) || 'selected session',\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"h-80\",\n                        children: pnlChartData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                            width: \"100%\",\n                            height: \"100%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                data: pnlChartData,\n                                margin: {\n                                    top: 5,\n                                    right: 20,\n                                    left: -25,\n                                    bottom: 5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.CartesianGrid, {\n                                        strokeDasharray: \"3 3\",\n                                        stroke: \"hsl(var(--border))\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.XAxis, {\n                                        dataKey: \"date\",\n                                        stroke: \"hsl(var(--muted-foreground))\",\n                                        fontSize: 12,\n                                        tickLine: false,\n                                        axisLine: false\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.YAxis, {\n                                        stroke: \"hsl(var(--muted-foreground))\",\n                                        fontSize: 12,\n                                        tickLine: false,\n                                        axisLine: false,\n                                        tickFormatter: (value)=>\"\".concat(value.toFixed(2))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.Tooltip, {\n                                        content: (param)=>{\n                                            let { active, payload, label } = param;\n                                            if (active && payload && payload.length) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-card border border-border rounded-lg p-3 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Date: \".concat(label)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"P/L: \"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: payload[0].value >= 0 ? 'text-green-600' : 'text-red-600',\n                                                                    children: [\n                                                                        payload[0].value,\n                                                                        \" \",\n                                                                        selectedSessionConfig.crypto2 || \"Crypto 2\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                \"Trade #\",\n                                                                payload[0].payload.trade\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 27\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, void 0);\n                                            }\n                                            return null;\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"pnl\",\n                                        stroke: \"hsl(var(--primary))\",\n                                        strokeWidth: 2,\n                                        dot: {\n                                            fill: 'hsl(var(--primary))',\n                                            strokeWidth: 2,\n                                            r: 4\n                                        },\n                                        activeDot: {\n                                            r: 6,\n                                            stroke: 'hsl(var(--primary))',\n                                            strokeWidth: 2\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Percent_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No sell trades recorded yet for this session.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Chart will appear after first profitable trade.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareAnalytics.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAwareAnalytics, \"nUx++1OgfgHw76GRC/umR4thJFw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_6__.useTradingContext\n    ];\n});\n_c = SessionAwareAnalytics;\nvar _c;\n$RefreshReg$(_c, \"SessionAwareAnalytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SessionAwareAnalytics.tsx\n"));

/***/ })

});