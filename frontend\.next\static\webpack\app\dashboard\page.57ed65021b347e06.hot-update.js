"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    // Show placeholder text when cryptos are not selected\n    const displayPair = config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : \"Crypto 1/Crypto 2\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayPair,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"/gxybvPIHrqyOR+dYDWY4VFMpPk=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});