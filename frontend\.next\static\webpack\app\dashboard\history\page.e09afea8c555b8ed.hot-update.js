"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/SessionAwareHistory.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAwareHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,FileSpreadsheet,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionAwareHistory() {\n    _s();\n    const { dispatch, orderHistory, config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('current');\n    const [selectedSessionHistory, setSelectedSessionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            loadSessions();\n        }\n    }[\"SessionAwareHistory.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAwareHistory.useEffect\": ()=>{\n            if (selectedSessionId === 'current') {\n                setSelectedSessionHistory(orderHistory);\n            } else {\n                const sessionHistory = sessionManager.getSessionHistory(selectedSessionId);\n                setSelectedSessionHistory(sessionHistory);\n            }\n        }\n    }[\"SessionAwareHistory.useEffect\"], [\n        selectedSessionId,\n        orderHistory\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleClearHistory = ()=>{\n        if (selectedSessionId === 'current') {\n            dispatch({\n                type: 'CLEAR_ORDER_HISTORY'\n            });\n            toast({\n                title: \"History Cleared\",\n                description: \"Current session trade history has been cleared.\"\n            });\n        } else {\n            // For past sessions, we would need to implement session history clearing\n            toast({\n                title: \"Cannot Clear\",\n                description: \"Cannot clear history for past sessions. Use current session to clear history.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportHistory = ()=>{\n        if (selectedSessionHistory.length === 0) {\n            toast({\n                title: \"No Data to Export\",\n                description: \"There is no trade history to export for the selected session.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        let csvContent;\n        let filename;\n        if (selectedSessionId === 'current') {\n            // Use existing export logic for current session\n            const headers = [\n                'Date',\n                'Time',\n                'Pair',\n                'Crypto',\n                'Order Type',\n                'Amount',\n                'Avg Price',\n                'Value',\n                'Price 1',\n                'Crypto 1',\n                'Price 2',\n                'Crypto 2',\n                'Profit/Loss (Crypto1)',\n                'Profit/Loss (Crypto2)'\n            ];\n            csvContent = [\n                headers.join(','),\n                ...selectedSessionHistory.map((entry)=>{\n                    var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                    return [\n                        new Date(entry.timestamp).toISOString().split('T')[0],\n                        new Date(entry.timestamp).toTimeString().split(' ')[0],\n                        entry.pair,\n                        entry.crypto1Symbol,\n                        entry.orderType,\n                        ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(config.numDigits)) || '',\n                        ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(config.numDigits)) || '',\n                        ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(config.numDigits)) || '',\n                        entry.crypto1Symbol,\n                        ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(config.numDigits)) || '',\n                        entry.crypto2Symbol,\n                        ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(config.numDigits)) || '',\n                        ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(config.numDigits)) || ''\n                    ].join(',');\n                })\n            ].join('\\n');\n            filename = \"current_session_history_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        } else {\n            // Use session manager export for past sessions\n            csvContent = sessionManager.exportSessionToCSV(selectedSessionId);\n            const session = sessionManager.loadSession(selectedSessionId);\n            filename = \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\");\n        }\n        if (!csvContent) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to generate CSV content.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create and download file\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', filename);\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Trade history has been exported to CSV file.\"\n        });\n    };\n    const getSelectedSessionInfo = ()=>{\n        if (selectedSessionId === 'current') {\n            return {\n                name: 'Current Session',\n                pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                totalTrades: orderHistory.length,\n                totalProfitLoss: orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0),\n                lastModified: Date.now(),\n                isActive: true\n            };\n        }\n        return sessions.find((s)=>s.id === selectedSessionId);\n    };\n    const selectedSession = getSelectedSessionInfo();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"Session History\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"View trading history for current and past sessions.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 items-start sm:items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium mb-2 block\",\n                                                children: \"Select Session:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                value: selectedSessionId,\n                                                onValueChange: setSelectedSessionId,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                        className: \"w-full sm:w-[300px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                            placeholder: \"Select a session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: \"current\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: \"default\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Current Session (\",\n                                                                                config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : 'Crypto 1/Crypto 2 = 0',\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                                                        className: \"my-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                            value: session.id,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: session.isActive ? \"default\" : \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: session.isActive ? \"Current\" : \"Past\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 182,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: session.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                        lineNumber: 185,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                                lineNumber: 181,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, session.id, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                                            lineNumber: 180,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleClearHistory,\n                                                className: \"btn-outline-neo\",\n                                                disabled: selectedSessionId !== 'current',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Clear History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleExportHistory,\n                                                className: \"btn-outline-neo\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            selectedSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted/50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Session:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Pair:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.pair\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total Trades:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: selectedSession.totalTrades\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Total P/L:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(selectedSession.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        children: selectedSession.totalProfitLoss.toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedSessionId !== 'current' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Last modified: \",\n                                            (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(selectedSession.lastModified), 'MMM dd, yyyy HH:mm')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-lg font-bold text-primary\",\n                                children: [\n                                    \"Trade History - \",\n                                    (selectedSession === null || selectedSession === void 0 ? void 0 : selectedSession.name) || 'Unknown Session'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: selectedSessionHistory.length === 0 ? \"No trades recorded for this session yet.\" : \"Showing \".concat(selectedSessionHistory.length, \" trades for the selected session.\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionHistoryTable, {\n                            history: selectedSessionHistory,\n                            config: config\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAwareHistory, \"pWp7ZU6XY5YR2imJ+wjTWvIkU9c=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_7__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionAwareHistory;\n// Custom history table component for session-specific history\nfunction SessionHistoryTable(param) {\n    let { history, config } = param;\n    const formatNum = (num)=>{\n        var _num_toFixed;\n        return (_num_toFixed = num === null || num === void 0 ? void 0 : num.toFixed(config.numDigits)) !== null && _num_toFixed !== void 0 ? _num_toFixed : '-';\n    };\n    const columns = [\n        {\n            key: \"date\",\n            label: \"Date\"\n        },\n        {\n            key: \"hour\",\n            label: \"Hour\"\n        },\n        {\n            key: \"pair\",\n            label: \"Couple\"\n        },\n        {\n            key: \"crypto\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"orderType\",\n            label: \"Order Type\"\n        },\n        {\n            key: \"amount\",\n            label: \"Amount\"\n        },\n        {\n            key: \"avgPrice\",\n            label: \"Avg Price\"\n        },\n        {\n            key: \"value\",\n            label: \"Value (\".concat(config.crypto2, \")\")\n        },\n        {\n            key: \"price1\",\n            label: \"Price 1\"\n        },\n        {\n            key: \"crypto1Symbol\",\n            label: \"Crypto (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"price2\",\n            label: \"Price 2\"\n        },\n        {\n            key: \"crypto2Symbol\",\n            label: \"Crypto (\".concat(config.crypto2, \")\")\n        },\n        {\n            key: \"profitCrypto1\",\n            label: \"Profit/Loss (\".concat(config.crypto1, \")\")\n        },\n        {\n            key: \"profitCrypto2\",\n            label: \"Profit/Loss (\".concat(config.crypto2, \")\")\n        }\n    ];\n    if (history.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_FileSpreadsheet_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No trading history for this session yet.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"min-w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"bg-card hover:bg-card border-b\",\n                            children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left\",\n                                    children: col.label\n                                }, col.key, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: history.map((entry)=>{\n                            var _formatNum;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-card/80 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(entry.timestamp), 'yyyy-MM-dd')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(entry.timestamp), 'HH:mm:ss')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.pair\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs font-semibold \".concat(entry.orderType === \"BUY\" ? \"text-green-400\" : \"text-destructive\"),\n                                        children: entry.orderType\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.amountCrypto1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.avgPrice)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.valueCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: formatNum(entry.price1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto1Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: (_formatNum = formatNum(entry.price2)) !== null && _formatNum !== void 0 ? _formatNum : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs\",\n                                        children: entry.crypto2Symbol\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs \".concat(entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 > 0 ? \"text-green-400\" : entry.realizedProfitLossCrypto1 && entry.realizedProfitLossCrypto1 < 0 ? \"text-destructive\" : \"\"),\n                                        children: formatNum(entry.realizedProfitLossCrypto1)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-3 py-2 text-xs \".concat(entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 > 0 ? \"text-green-400\" : entry.realizedProfitLossCrypto2 && entry.realizedProfitLossCrypto2 < 0 ? \"text-destructive\" : \"\"),\n                                        children: formatNum(entry.realizedProfitLossCrypto2)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, entry.id, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\SessionAwareHistory.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SessionHistoryTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"SessionAwareHistory\");\n$RefreshReg$(_c1, \"SessionHistoryTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SessionAwareHistory.tsx\n"));

/***/ })

});