"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Default fallback value\n    return 1.0;\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0],\n    crypto2: (_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_QUOTES_SIMPLE[_lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_CRYPTOS[0]] || DEFAULT_QUOTE_CURRENCIES)[0],\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Reset all target price rows and clear history\n            const resetTargetPriceRows = state.targetPriceRows.map((row)=>({\n                    ...row,\n                    status: 'Free',\n                    orderLevel: 0,\n                    valueLevel: state.config.baseBid,\n                    crypto1AmountHeld: undefined,\n                    originalCostCrypto2: undefined,\n                    crypto1Var: undefined,\n                    crypto2Var: undefined,\n                    lastActionTimestamp: undefined\n                }));\n            lastActionTimestampPerCounter.clear();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: resetTargetPriceRows,\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    dispatch({\n                        type: 'FLUCTUATE_MARKET_PRICE'\n                    });\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Only check essential conditions\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0) {\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Only show toast notifications for actual state changes, not initial state\n                    if (!isInitial) {\n                        if (!isOnline) {\n                            toast({\n                                title: \"Network Disconnected\",\n                                description: \"You are currently offline. Data will be saved locally.\",\n                                variant: \"destructive\",\n                                duration: 5000\n                            });\n                        } else {\n                            toast({\n                                title: \"Network Reconnected\",\n                                description: \"Connection restored. Auto-saving enabled.\",\n                                duration: 3000\n                            });\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1284,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"ptlEOb1DUxVxun/70+rIAApLOXs=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});