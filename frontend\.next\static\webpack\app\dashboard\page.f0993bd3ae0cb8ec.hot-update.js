"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async checkBackendConnection() {\n        try {\n            // Try a simple ping to the backend\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 1500); // 1.5 second timeout\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                console.log('✅ Session Manager: Backend connection established');\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            this.useBackend = false;\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage fallback');\n            console.warn('💡 To enable backend features, start the backend server: python run.py');\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            const sessionsKey = this.getWindowSpecificKey(SESSIONS_STORAGE_KEY);\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const sessionsData = localStorage.getItem(sessionsKey);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions for window \").concat(this.windowId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            const sessionsKey = this.getWindowSpecificKey(SESSIONS_STORAGE_KEY);\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(sessionsKey, JSON.stringify(sessionsObject));\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.id;\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime if session is currently running\n            let currentRuntime = session.runtime;\n            const startTime = this.sessionStartTimes.get(sessionId);\n            if (startTime && isActive) {\n                // Session is running, update runtime\n                currentRuntime = session.runtime + (Date.now() - startTime);\n                // Reset start time for next interval\n                this.sessionStartTimes.set(sessionId, Date.now());\n            } else if (!isActive && startTime) {\n                // Session stopped, finalize runtime\n                currentRuntime = session.runtime + (Date.now() - startTime);\n                this.sessionStartTimes.delete(sessionId);\n            } else if (isActive && !startTime) {\n                // Session just started, record start time\n                this.sessionStartTimes.set(sessionId, Date.now());\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        const sessionsKey = this.getWindowSpecificKey(SESSIONS_STORAGE_KEY);\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(sessionsKey);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        this.loadSessionsFromStorage();\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Start with localStorage mode, check backend in background\n        this.useBackend = false;\n        // Check backend connection in background without blocking\n        setTimeout(()=>{\n            this.checkBackendConnection().catch(()=>{\n            // Silently fail and continue with localStorage\n            });\n        }, 1000);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});