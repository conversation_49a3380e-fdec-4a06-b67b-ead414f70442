"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/network-monitor.ts":
/*!************************************!*\
  !*** ./src/lib/network-monitor.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSaveManager: () => (/* binding */ AutoSaveManager),\n/* harmony export */   MemoryMonitor: () => (/* binding */ MemoryMonitor),\n/* harmony export */   NetworkMonitor: () => (/* binding */ NetworkMonitor)\n/* harmony export */ });\nclass NetworkMonitor {\n    static getInstance() {\n        if (!NetworkMonitor.instance) {\n            NetworkMonitor.instance = new NetworkMonitor();\n        }\n        return NetworkMonitor.instance;\n    }\n    setupEventListeners() {\n        window.addEventListener('online', this.handleOnline.bind(this));\n        window.addEventListener('offline', this.handleOffline.bind(this));\n        // Listen for visibility change to check connection when tab becomes active\n        document.addEventListener('visibilitychange', ()=>{\n            if (!document.hidden) {\n                this.checkConnection();\n            }\n        });\n    }\n    handleOnline() {\n        console.log('🌐 Network: Back online');\n        this.isOnline = true;\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.notifyListeners(true);\n    }\n    handleOffline() {\n        console.log('🌐 Network: Gone offline');\n        this.isOnline = false;\n        this.notifyListeners(false);\n    }\n    async checkConnection() {\n        try {\n            // Try to fetch a small resource to verify actual connectivity\n            const response = await fetch('/api/health', {\n                method: 'HEAD',\n                cache: 'no-cache',\n                signal: AbortSignal.timeout(5000) // 5 second timeout\n            });\n            const isConnected = response.ok;\n            if (isConnected !== this.isOnline) {\n                this.isOnline = isConnected;\n                this.notifyListeners(isConnected);\n                if (isConnected) {\n                    this.lastOnlineTime = Date.now();\n                    this.reconnectAttempts = 0;\n                }\n            }\n            return isConnected;\n        } catch (error) {\n            // If fetch fails, we're likely offline\n            if (this.isOnline) {\n                this.isOnline = false;\n                this.notifyListeners(false);\n            }\n            return false;\n        }\n    }\n    startPeriodicCheck() {\n        // Use a more efficient interval with cleanup\n        const interval = setInterval(()=>{\n            this.checkConnection();\n        }, 30000); // Check every 30 seconds\n        // Store interval for cleanup\n        this.periodicInterval = interval;\n    }\n    cleanup() {\n        if (this.periodicInterval) {\n            clearInterval(this.periodicInterval);\n        }\n        this.listeners.clear();\n    }\n    notifyListeners(isOnline) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(isOnline);\n            } catch (error) {\n                console.error('Error in network status listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.listeners.delete(listener);\n        };\n    }\n    getStatus() {\n        return {\n            isOnline: this.isOnline,\n            lastOnlineTime: this.lastOnlineTime,\n            reconnectAttempts: this.reconnectAttempts\n        };\n    }\n    async forceCheck() {\n        return await this.checkConnection();\n    }\n    // Attempt to reconnect with exponential backoff\n    async attemptReconnect() {\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.log('🌐 Network: Max reconnect attempts reached');\n            return false;\n        }\n        this.reconnectAttempts++;\n        const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000);\n        console.log(\"\\uD83C\\uDF10 Network: Attempting reconnect \".concat(this.reconnectAttempts, \"/\").concat(this.maxReconnectAttempts, \" in \").concat(delay, \"ms\"));\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n        const isConnected = await this.checkConnection();\n        if (!isConnected && this.reconnectAttempts < this.maxReconnectAttempts) {\n            // Schedule next attempt\n            setTimeout(()=>this.attemptReconnect(), 1000);\n        }\n        return isConnected;\n    }\n    constructor(){\n        this.isOnline = navigator.onLine;\n        this.listeners = new Set();\n        this.lastOnlineTime = Date.now();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectInterval = 5000 // 5 seconds\n        ;\n        this.hasInitialized = false;\n        this.setupEventListeners();\n        this.startPeriodicCheck();\n    }\n}\n// Auto-save functionality\nclass AutoSaveManager {\n    static getInstance() {\n        if (!AutoSaveManager.instance) {\n            AutoSaveManager.instance = new AutoSaveManager();\n        }\n        return AutoSaveManager.instance;\n    }\n    setupNetworkListener() {\n        this.networkMonitor.addListener((isOnline)=>{\n            if (isOnline && this.saveFunction) {\n                // Save immediately when coming back online\n                console.log('💾 Auto-save: Saving on network reconnection');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    setupBeforeUnloadListener() {\n        window.addEventListener('beforeunload', ()=>{\n            if (this.saveFunction) {\n                console.log('💾 Auto-save: Saving before page unload');\n                this.saveFunction();\n            }\n        });\n        // Also save on page visibility change (when user switches tabs)\n        document.addEventListener('visibilitychange', ()=>{\n            if (document.hidden && this.saveFunction) {\n                console.log('💾 Auto-save: Saving on tab switch');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        });\n    }\n    enable(saveFunction) {\n        let intervalMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30000;\n        this.saveFunction = saveFunction;\n        this.intervalMs = intervalMs;\n        this.isEnabled = true;\n        this.stop(); // Clear any existing interval\n        this.saveInterval = setInterval(()=>{\n            if (this.isEnabled && this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n                console.log('💾 Auto-save: Periodic save');\n                this.saveFunction();\n                this.lastSaveTime = Date.now();\n            }\n        }, this.intervalMs);\n        console.log(\"\\uD83D\\uDCBE Auto-save: Enabled with \".concat(intervalMs, \"ms interval\"));\n    }\n    disable() {\n        this.isEnabled = false;\n        this.stop();\n        console.log('💾 Auto-save: Disabled');\n    }\n    stop() {\n        if (this.saveInterval) {\n            clearInterval(this.saveInterval);\n            this.saveInterval = null;\n        }\n    }\n    saveNow() {\n        if (this.saveFunction && this.networkMonitor.getStatus().isOnline) {\n            console.log('💾 Auto-save: Manual save triggered');\n            this.saveFunction();\n            this.lastSaveTime = Date.now();\n        }\n    }\n    getStatus() {\n        return {\n            isEnabled: this.isEnabled,\n            lastSaveTime: this.lastSaveTime,\n            intervalMs: this.intervalMs,\n            isOnline: this.networkMonitor.getStatus().isOnline\n        };\n    }\n    constructor(){\n        this.saveInterval = null;\n        this.saveFunction = null;\n        this.intervalMs = 30000 // 30 seconds default\n        ;\n        this.isEnabled = true;\n        this.lastSaveTime = 0;\n        this.networkMonitor = NetworkMonitor.getInstance();\n        this.setupNetworkListener();\n        this.setupBeforeUnloadListener();\n    }\n}\n// Memory usage monitor to prevent memory leaks\nclass MemoryMonitor {\n    static getInstance() {\n        if (!MemoryMonitor.instance) {\n            MemoryMonitor.instance = new MemoryMonitor();\n        }\n        return MemoryMonitor.instance;\n    }\n    startMonitoring() {\n        // Only monitor if performance.memory is available (Chrome)\n        if ('memory' in performance) {\n            this.checkInterval = setInterval(()=>{\n                this.checkMemoryUsage();\n            }, 60000); // Check every minute\n        }\n    }\n    checkMemoryUsage() {\n        if ('memory' in performance) {\n            const memory = performance.memory;\n            const usedJSHeapSize = memory.usedJSHeapSize;\n            this.notifyListeners(memory);\n            if (usedJSHeapSize > this.criticalThreshold) {\n                console.warn('🧠 Memory: Critical memory usage detected:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    limit: \"\".concat((memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2), \"MB\")\n                });\n                // Trigger garbage collection if possible\n                if ('gc' in window) {\n                    window.gc();\n                }\n            } else if (usedJSHeapSize > this.warningThreshold) {\n                console.log('🧠 Memory: High memory usage:', {\n                    used: \"\".concat((usedJSHeapSize / 1024 / 1024).toFixed(2), \"MB\"),\n                    total: \"\".concat((memory.totalJSHeapSize / 1024 / 1024).toFixed(2), \"MB\")\n                });\n            }\n        }\n    }\n    notifyListeners(memory) {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(memory);\n            } catch (error) {\n                console.error('Error in memory monitor listener:', error);\n            }\n        });\n    }\n    addListener(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    getMemoryUsage() {\n        if ('memory' in performance) {\n            return performance.memory;\n        }\n        return null;\n    }\n    stop() {\n        if (this.checkInterval) {\n            clearInterval(this.checkInterval);\n            this.checkInterval = null;\n        }\n    }\n    constructor(){\n        this.checkInterval = null;\n        this.warningThreshold = 100 * 1024 * 1024 // 100MB\n        ;\n        this.criticalThreshold = 200 * 1024 * 1024 // 200MB\n        ;\n        this.listeners = new Set();\n        this.startMonitoring();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/network-monitor.ts\n"));

/***/ })

});