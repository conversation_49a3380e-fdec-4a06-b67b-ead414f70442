"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/ui/crypto-input.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/crypto-input.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoInput: () => (/* binding */ CryptoInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CryptoInput auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CryptoInput(param) {\n    let { label, value, allowedCryptos, onValidCrypto, placeholder = \"Enter crypto symbol\", description, className } = param;\n    _s();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [validationState, setValidationState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [userValidated, setUserValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track if user has validated this crypto\n    const handleCheck = ()=>{\n        const upperCaseInput = inputValue.toUpperCase().trim();\n        if (!upperCaseInput) {\n            setValidationState('invalid');\n            setErrorMessage('Please enter a crypto symbol');\n            return;\n        }\n        if (!allowedCryptos || !Array.isArray(allowedCryptos)) {\n            setValidationState('invalid');\n            setErrorMessage('No allowed cryptocurrencies configured');\n            return;\n        }\n        if (allowedCryptos.includes(upperCaseInput)) {\n            setValidationState('valid');\n            setErrorMessage('');\n            setUserValidated(true); // Mark as user validated\n            onValidCrypto(upperCaseInput);\n        } else {\n            setValidationState('invalid');\n            setErrorMessage(\"\".concat(upperCaseInput, \" is not available. Allowed: \").concat(allowedCryptos.join(', ')));\n        }\n    };\n    const handleInputChange = (e)=>{\n        setInputValue(e.target.value);\n        setValidationState('idle');\n        setErrorMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter') {\n            handleCheck();\n        }\n    };\n    const getValidationIcon = ()=>{\n        switch(validationState){\n            case 'valid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 16\n                }, this);\n            case 'invalid':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getValidationColor = ()=>{\n        switch(validationState){\n            case 'valid':\n                return 'border-green-500';\n            case 'invalid':\n                return 'border-red-500';\n            default:\n                return '';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"space-y-2\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                htmlFor: \"crypto-input-\".concat(label),\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                id: \"crypto-input-\".concat(label),\n                                value: inputValue,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: placeholder,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"pr-8\", getValidationColor())\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            validationState !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                children: getValidationIcon()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleCheck,\n                        variant: \"outline\",\n                        className: \"btn-neo\",\n                        disabled: !inputValue.trim(),\n                        children: \"Check\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            value && userValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-600 font-medium\",\n                        children: [\n                            \"Selected: \",\n                            value\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this),\n            validationState === 'invalid' && errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-2 text-sm text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: errorMessage\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: description\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-muted-foreground\",\n                children: [\n                    allowedCryptos && Array.isArray(allowedCryptos) ? allowedCryptos.length : 0,\n                    \" cryptocurrencies available\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\crypto-input.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(CryptoInput, \"MnFNn7IY0g3RG/36abcLlvksZ20=\");\n_c = CryptoInput;\nvar _c;\n$RefreshReg$(_c, \"CryptoInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/crypto-input.tsx\n"));

/***/ })

});