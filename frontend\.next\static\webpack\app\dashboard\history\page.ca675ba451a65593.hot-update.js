"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/BalancesDisplay.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BalancesDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction BalancesDisplay() {\n    _s();\n    const { crypto1Balance, crypto2Balance, stablecoinBalance, config, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const [editingBalance, setEditingBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tempValues, setTempValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        crypto1: crypto1Balance.toString(),\n        crypto2: crypto2Balance.toString(),\n        stablecoin: stablecoinBalance.toString()\n    });\n    const formatBalance = (balance)=>balance.toFixed(config.numDigits);\n    const handleEdit = (balanceType)=>{\n        setEditingBalance(balanceType);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const handleSave = (balanceType)=>{\n        const newValue = parseFloat(tempValues[balanceType]);\n        if (!isNaN(newValue) && newValue >= 0) {\n            if (balanceType === 'crypto1') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: newValue,\n                        crypto2: crypto2Balance\n                    }\n                });\n            } else if (balanceType === 'crypto2') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: crypto1Balance,\n                        crypto2: newValue\n                    }\n                });\n            } else if (balanceType === 'stablecoin') {\n                dispatch({\n                    type: 'UPDATE_STABLECOIN_BALANCE',\n                    payload: newValue\n                });\n            }\n        }\n        setEditingBalance(null);\n    };\n    const handleCancel = ()=>{\n        setEditingBalance(null);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const renderBalanceCard = (title, balance, balanceType, icon, currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 9\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: editingBalance === balanceType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"number\",\n                                value: tempValues[balanceType],\n                                onChange: (e)=>setTempValues((prev)=>({\n                                            ...prev,\n                                            [balanceType]: e.target.value\n                                        })),\n                                className: \"text-lg font-bold\",\n                                step: \"any\",\n                                min: \"0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        onClick: ()=>handleSave(balanceType),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Save\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cancel\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: formatBalance(balance)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Available \",\n                                            currency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>handleEdit(balanceType),\n                                className: \"ml-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid gap-4 md:grid-cols-3 mb-6\",\n        children: [\n            renderBalanceCard(\"\".concat(config.crypto1 || \"Crypto 1\", \" Balance\"), crypto1Balance, 'crypto1', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this), config.crypto1 || \"Crypto 1\"),\n            renderBalanceCard(\"\".concat(config.crypto2 || \"Crypto 2\", \" Balance\"), crypto2Balance, 'crypto2', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this), config.crypto2 || \"Crypto 2\"),\n            renderBalanceCard(\"Stablecoin Balance (\".concat(config.preferredStablecoin || 'N/A', \")\"), stablecoinBalance, 'stablecoin', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5 text-primary\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this), 'Stablecoins')\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(BalancesDisplay, \"NTICQWigKma+9pPtsoUOoa+lf24=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = BalancesDisplay;\nvar _c;\n$RefreshReg$(_c, \"BalancesDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\n"));

/***/ })

});