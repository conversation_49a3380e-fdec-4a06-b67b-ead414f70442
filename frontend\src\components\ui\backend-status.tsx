"use client";

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Wifi, WifiOff, Refresh<PERSON>w, AlertTriangle, Clock } from 'lucide-react';

interface BackendStatusProps {
  className?: string;
  showDetails?: boolean;
}

interface NetworkStatusProps {
  className?: string;
}

export function NetworkStatus({ className = "" }: NetworkStatusProps) {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(timeInterval);
    };
  }, []);

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge
        variant={isOnline ? "default" : "destructive"}
        className="flex items-center gap-1"
      >
        {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
        {isOnline ? "Online" : "Offline"}
      </Badge>
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <Clock className="h-3 w-3" />
        <span>{currentTime.toLocaleTimeString()}</span>
      </div>
    </div>
  );
}

export function BackendStatus({ className = "", showDetails = false }: BackendStatusProps) {
  const [isOnline, setIsOnline] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkBackendStatus = async () => {
    setIsChecking(true);
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000);

      const response = await fetch('http://localhost:5000/', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      setIsOnline(response.status < 500);
      setLastCheck(new Date());
    } catch (error) {
      setIsOnline(false);
      setLastCheck(new Date());
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkBackendStatus();

    // Check every 30 seconds
    const interval = setInterval(checkBackendStatus, 30000);

    return () => clearInterval(interval);
  }, []);

  if (!showDetails) {
    return (
      <Badge
        variant={isOnline ? "default" : "destructive"}
        className={`flex items-center gap-1 ${className}`}
      >
        {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
        {isOnline ? "Backend Online" : "Backend Offline"}
      </Badge>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="font-medium">
              Backend Status: {isOnline ? "Online" : "Offline"}
            </span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={checkBackendStatus}
            disabled={isChecking}
          >
            {isChecking ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
            Check
          </Button>
        </div>
        
        {lastCheck && (
          <p className="text-xs text-muted-foreground mt-2">
            Last checked: {lastCheck.toLocaleTimeString()}
          </p>
        )}
        
        {!isOnline && (
          <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-yellow-600 mb-1">Backend Offline</p>
                <p className="text-yellow-700">
                  Some features may be limited. Session management will use local storage.
                </p>
                <p className="text-xs text-yellow-600 mt-2">
                  To start the backend: <code className="bg-yellow-100 px-1 rounded">python run.py</code>
                </p>
              </div>
            </div>
          </div>
        )}
        
        {isOnline && (
          <div className="mt-3 p-3 bg-green-500/10 border border-green-500/20 rounded-md">
            <div className="flex items-center gap-2">
              <Wifi className="h-4 w-4 text-green-600" />
              <p className="text-sm text-green-700">
                All features available. Backend connected successfully.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
