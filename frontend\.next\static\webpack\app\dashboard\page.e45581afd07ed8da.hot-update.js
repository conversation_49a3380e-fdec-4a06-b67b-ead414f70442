"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config, saveCurrentSession } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [currentSessionName, setCurrentSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            const updateSessionName = {\n                \"DashboardOrdersPage.useEffect.updateSessionName\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        const session = sessionManager.loadSession(currentSessionId);\n                        if (session) {\n                            setCurrentSessionName(session.name);\n                            return;\n                        }\n                    }\n                    // No session or session not found, generate default name\n                    if (config.crypto1 && config.crypto2) {\n                        const defaultName = \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot');\n                        setCurrentSessionName(defaultName);\n                    } else {\n                        setCurrentSessionName('Crypto 1/Crypto 2 = 0');\n                    }\n                }\n            }[\"DashboardOrdersPage.useEffect.updateSessionName\"];\n            updateSessionName();\n        }\n    }[\"DashboardOrdersPage.useEffect\"], [\n        config.crypto1,\n        config.crypto2,\n        config.tradingMode,\n        sessionManager,\n        targetPriceRows.length,\n        orderHistory.length\n    ]);\n    // Show placeholder text when cryptos are not selected\n    const displayTitle = currentSessionName || (config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot') : \"Crypto 1/Crypto 2 = 0\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayTitle,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"s8H83B6nhFivw+hj+iqQivUZg/s=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});