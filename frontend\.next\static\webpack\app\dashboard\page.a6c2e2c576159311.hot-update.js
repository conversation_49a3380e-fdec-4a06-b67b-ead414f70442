"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    const [currentSessionName, setCurrentSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            const updateSessionName = {\n                \"DashboardOrdersPage.useEffect.updateSessionName\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        const session = sessionManager.loadSession(currentSessionId);\n                        if (session) {\n                            setCurrentSessionName(session.name);\n                            return;\n                        }\n                    }\n                    // No session or session not found, generate default name\n                    if (config.crypto1 && config.crypto2) {\n                        const defaultName = \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot');\n                        setCurrentSessionName(defaultName);\n                    } else {\n                        setCurrentSessionName('Crypto 1/Crypto 2 = 0');\n                    }\n                }\n            }[\"DashboardOrdersPage.useEffect.updateSessionName\"];\n            updateSessionName();\n        }\n    }[\"DashboardOrdersPage.useEffect\"], [\n        config.crypto1,\n        config.crypto2,\n        config.tradingMode,\n        sessionManager\n    ]);\n    // Show placeholder text when cryptos are not selected\n    const displayTitle = currentSessionName || (config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot') : \"Crypto 1/Crypto 2 = 0\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayTitle,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"7pd+MDYd4t3/qdnnntqwNdX1bm8=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});