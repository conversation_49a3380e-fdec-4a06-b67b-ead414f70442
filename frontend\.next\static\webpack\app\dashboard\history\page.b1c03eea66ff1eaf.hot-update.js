"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    dispatch({\n                        type: 'FLUCTUATE_MARKET_PRICE'\n                    });\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Only check essential conditions\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0) {\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Auto-create session when bot starts if none exists\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration\n                if (state.config.crypto1 && state.config.crypto2) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Only show toast notifications for actual state changes, not initial state\n                    if (!isInitial) {\n                        if (!isOnline) {\n                            toast({\n                                title: \"Network Disconnected\",\n                                description: \"You are currently offline. Data will be saved locally.\",\n                                variant: \"destructive\",\n                                duration: 5000\n                            });\n                        } else {\n                            toast({\n                                title: \"Network Reconnected\",\n                                description: \"Connection restored. Auto-saving enabled.\",\n                                duration: 3000\n                            });\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1322,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"2sxZUEy/1e6tN94saVrJ2VZsG+c=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9UcmFkaW5nQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUVxRztBQUVpQjtBQUNsRjtBQUVwQyx3Q0FBd0M7QUFDeEMsTUFBTVcsMkJBQTJCO0lBQUM7SUFBUTtJQUFRO0NBQU07QUFDWDtBQUNOO0FBQ2dCO0FBQ2dDO0FBOEN2RiwwREFBMEQ7QUFDMUQsTUFBTU8sOEJBQThCLENBQUNDO0lBQ25DLDRDQUE0QztJQUM1QyxJQUFJLENBQUNBLE9BQU9DLE9BQU8sSUFBSSxDQUFDRCxPQUFPRSxPQUFPLEVBQUU7UUFDdEMsT0FBTztJQUNUO0lBQ0EseUNBQXlDO0lBQ3pDLE9BQU9DLDZCQUE2Qkg7QUFDdEM7QUFFQSxpRUFBaUU7QUFDakUsTUFBTUksd0JBQXdCLE9BQU9KO0lBQ25DLElBQUk7UUFDRiw0Q0FBNEM7UUFDNUMsSUFBSSxDQUFDQSxPQUFPQyxPQUFPLElBQUksQ0FBQ0QsT0FBT0UsT0FBTyxFQUFFO1lBQ3RDLE9BQU87UUFDVDtRQUNBLGlEQUFpRDtRQUNqRCxNQUFNRyxTQUFTLEdBQW9CTCxPQUFqQkEsT0FBT0MsT0FBTyxFQUFrQixPQUFmRCxPQUFPRSxPQUFPLEVBQUdJLFdBQVc7UUFFL0Qsd0JBQXdCO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sc0RBQTZELE9BQVBIO1lBQ25GLElBQUlFLFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDLE1BQU1DLFFBQVFDLFdBQVdILEtBQUtFLEtBQUs7Z0JBQ25DLElBQUlBLFFBQVEsR0FBRztvQkFDYkUsUUFBUUMsR0FBRyxDQUFDLGlDQUFtRGYsT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUF1QlcsT0FBcEJaLE9BQU9FLE9BQU8sRUFBQyxPQUFXLE9BQU5VO29CQUNuRixPQUFPQTtnQkFDVDtZQUNGO1FBQ0YsRUFBRSxPQUFPSSxjQUFjO1lBQ3JCRixRQUFRRyxJQUFJLENBQUMsNkNBQTZDRDtRQUM1RDtRQUVBLHFEQUFxRDtRQUNyRCxJQUFJO1lBQ0YsTUFBTUUsWUFBWUMsZUFBZW5CLE9BQU9DLE9BQU87WUFDL0MsTUFBTW1CLFlBQVlELGVBQWVuQixPQUFPRSxPQUFPO1lBRS9DLElBQUlnQixhQUFhRSxXQUFXO2dCQUMxQixNQUFNYixXQUFXLE1BQU1DLE1BQU0scURBQWdGWSxPQUEzQkYsV0FBVSxtQkFBMkIsT0FBVkU7Z0JBQzdHLElBQUliLFNBQVNFLEVBQUUsRUFBRTt3QkFFREM7b0JBRGQsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO29CQUNoQyxNQUFNQyxTQUFRRixrQkFBQUEsSUFBSSxDQUFDUSxVQUFVLGNBQWZSLHNDQUFBQSxlQUFpQixDQUFDVSxVQUFVO29CQUMxQyxJQUFJUixRQUFRLEdBQUc7d0JBQ2JFLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBcURmLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBdUJXLE9BQXBCWixPQUFPRSxPQUFPLEVBQUMsT0FBVyxPQUFOVTt3QkFDckYsT0FBT0E7b0JBQ1Q7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT1MsWUFBWTtZQUNuQlAsUUFBUUcsSUFBSSxDQUFDLDZDQUE2Q0k7UUFDNUQ7UUFFQSwrQkFBK0I7UUFDL0IsTUFBTUMsWUFBWW5CLDZCQUE2Qkg7UUFDL0NjLFFBQVFDLEdBQUcsQ0FBQyx3QkFBMENmLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBdUJxQixPQUFwQnRCLE9BQU9FLE9BQU8sRUFBQyxPQUFlLE9BQVZvQjtRQUMxRSxPQUFPQTtJQUVULEVBQUUsT0FBT0MsT0FBTztRQUNkVCxRQUFRUyxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPcEIsNkJBQTZCSDtJQUN0QztBQUNGO0FBRUEseURBQXlEO0FBQ3pELE1BQU1tQixpQkFBaUIsQ0FBQ2Q7SUFDdEIsTUFBTW1CLFVBQXFDO1FBQ3pDLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsU0FBUztRQUNULFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsUUFBUTtRQUNSLE9BQU87SUFDVDtJQUNBLE9BQU9BLE9BQU8sQ0FBQ25CLE9BQU9DLFdBQVcsR0FBRyxJQUFJO0FBQzFDO0FBRUEsd0VBQXdFO0FBQ3hFLE1BQU1tQiw0QkFBNEIsT0FBT0MsUUFBZ0JDO0lBQ3ZELElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsSUFBSVIsZUFBZU8sV0FBV1AsZUFBZVEsYUFBYTtZQUN4RCxNQUFNQyxXQUFXVCxlQUFlTztZQUNoQyxNQUFNRyxlQUFlVixlQUFlUTtZQUVwQyxJQUFJQyxhQUFhQyxjQUFjLE9BQU8sS0FBSyxnQkFBZ0I7WUFFM0Qsd0NBQXdDO1lBQ3hDLE1BQU10QixXQUFXLE1BQU1DLE1BQU0scURBQStFcUIsT0FBMUJELFVBQVMsbUJBQThCLE9BQWJDO1lBQzVHLElBQUl0QixTQUFTRSxFQUFFLEVBQUU7b0JBRXlCQztnQkFEeEMsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQyxNQUFNbUIsT0FBT0YsWUFBWUMsZ0JBQWVuQixpQkFBQUEsSUFBSSxDQUFDa0IsU0FBUyxjQUFkbEIscUNBQUFBLGNBQWdCLENBQUNtQixhQUFhLEdBQUc7Z0JBQ3pFLElBQUlDLE9BQU8sR0FBRztvQkFDWmhCLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNZLE9BQVZELFFBQU8sS0FBbUJJLE9BQWhCSCxZQUFXLE9BQVUsT0FBTEc7b0JBQzdELE9BQU9BO2dCQUNUO1lBQ0Y7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNQyxpQkFBaUJDLFlBQVlOO1FBQ25DLE1BQU1PLHFCQUFxQkQsWUFBWUw7UUFDdkMsTUFBTUcsT0FBT0MsaUJBQWlCRTtRQUU5Qm5CLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMENZLE9BQVZELFFBQU8sS0FBbUJJLE9BQWhCSCxZQUFXLE9BQVUsT0FBTEcsTUFBSztRQUMzRSxPQUFPQTtJQUVULEVBQUUsT0FBT1AsT0FBTztRQUNkVCxRQUFRUyxLQUFLLENBQUMsNENBQTRDQTtRQUMxRCxpQkFBaUI7UUFDakIsTUFBTVEsaUJBQWlCQyxZQUFZTjtRQUNuQyxNQUFNTyxxQkFBcUJELFlBQVlMO1FBQ3ZDLE9BQU9JLGlCQUFpQkU7SUFDMUI7QUFDRjtBQUVBLGlGQUFpRjtBQUNqRixNQUFNRCxjQUFjLENBQUNOO0lBQ25CLE1BQU1RLFlBQXVDO1FBQzNDLHlCQUF5QjtRQUN6QixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsUUFBUTtRQUNSLFFBQVE7UUFDUixTQUFTO1FBQ1QsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsY0FBYztRQUNkLE9BQU87UUFDUCxRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsT0FBTztRQUNQLFNBQVM7UUFDVCxTQUFTO1FBQ1QsT0FBTztRQUNQLE9BQU87UUFFUCxzQkFBc0I7UUFDdEIsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLFFBQVE7UUFDUixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsdUJBQXVCO1FBQ3ZCLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBRVIscUJBQXFCO1FBQ3JCLFFBQVE7UUFDUixRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBRVAsMkJBQTJCO1FBQzNCLFNBQVM7UUFDVCxRQUFRO1FBQ1IsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUVQLGdCQUFnQjtRQUNoQixPQUFPO1FBQ1AsUUFBUTtRQUVSLGFBQWE7UUFDYixPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCxlQUFlO1FBQ2YsT0FBTztRQUNQLFFBQVE7UUFDUixTQUFTO1FBQ1QsT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFFUCxxQkFBcUI7UUFDckIsUUFBUTtRQUNSLE9BQU87UUFDUCxPQUFPO1FBQ1AsUUFBUTtRQUNSLE9BQU87UUFDUCxTQUFTO1FBQ1QsT0FBTztRQUNQLFFBQVE7UUFDUixNQUFNO1FBQ04sT0FBTztRQUNQLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE9BQU87UUFDUCxRQUFRO1FBQ1IsUUFBUTtRQUNSLE9BQU87UUFDUCxRQUFRO1FBRVIsY0FBYztRQUNkLFFBQVE7UUFDUixRQUFRO1FBQ1IsU0FBUztRQUNULFFBQVE7UUFDUixPQUFPO0lBQ1Q7SUFDQSxPQUFPQSxTQUFTLENBQUNSLE9BQU9wQixXQUFXLEdBQUcsSUFBSTtBQUM1QztBQUVBLHVGQUF1RjtBQUN2RixNQUFNSCwrQkFBK0IsQ0FBQ0g7SUFDcEMsTUFBTW1DLGtCQUFrQkgsWUFBWWhDLE9BQU9DLE9BQU87SUFDbEQsTUFBTW1DLGtCQUFrQkosWUFBWWhDLE9BQU9FLE9BQU87SUFFbEQscUVBQXFFO0lBQ3JFLE1BQU1tQyxZQUFZRixrQkFBa0JDO0lBRXBDLCtCQUErQjtJQUMvQixNQUFNRSxjQUFjLENBQUNDLEtBQUtDLE1BQU0sS0FBSyxHQUFFLElBQUssTUFBTSxNQUFNO0lBQ3hELE1BQU1DLGFBQWFKLFlBQWEsS0FBSUMsV0FBVTtJQUU5Q3hCLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBc0RvQixPQUFwQm5DLE9BQU9DLE9BQU8sRUFBQyxPQUEyQkQsT0FBdEJtQyxpQkFBZ0IsUUFBMEJDLE9BQXBCcEMsT0FBT0UsT0FBTyxFQUFDLE9BQTJCdUMsT0FBdEJMLGlCQUFnQixRQUE0QixPQUF0QkssV0FBV0MsT0FBTyxDQUFDO0lBRXJKLE9BQU9EO0FBQ1Q7QUF3QkEsTUFBTUUsb0JBQW1DO0lBQ3ZDQyxhQUFhO0lBQ2IzQyxTQUFTO0lBQ1RDLFNBQVM7SUFDVDJDLFNBQVM7SUFDVEMsWUFBWTtJQUNaQyxXQUFXO0lBQ1hDLGlCQUFpQjtJQUNqQkMsMkJBQTJCO0lBQzNCQywyQkFBMkI7SUFDM0JDLHFCQUFxQjlELDZEQUFxQixDQUFDLEVBQUU7QUFDL0M7QUFFQSxNQUFNK0Qsc0JBQW9DO0lBQ3hDcEQsUUFBUTJDO0lBQ1JVLGlCQUFpQixFQUFFO0lBQ25CQyxjQUFjLEVBQUU7SUFDaEJDLGFBQWFuRSw0REFBb0JBO0lBQ2pDb0Usb0JBQW9CekQsNEJBQTRCNEM7SUFDaEQsZ0NBQWdDO0lBQ2hDYyxpQkFBaUI7SUFDakJDLGdCQUFnQjtJQUNoQkMsZ0JBQWdCO0lBQ2hCQyxtQkFBbUI7SUFDbkJDLGVBQWU7QUFDakI7QUFFQSxNQUFNQyxnQ0FBZ0MsSUFBSUM7QUFFMUMscUNBQXFDO0FBQ3JDLE1BQU1DLGNBQWM7QUFFcEIsTUFBTUMsMEJBQTBCLENBQUNDO0lBQy9CLElBQUk7UUFDRixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsY0FBYztnQkFDbEJuRSxRQUFRa0UsTUFBTWxFLE1BQU07Z0JBQ3BCcUQsaUJBQWlCYSxNQUFNYixlQUFlO2dCQUN0Q0MsY0FBY1ksTUFBTVosWUFBWTtnQkFDaENDLGFBQWFXLE1BQU1YLFdBQVc7Z0JBQzlCQyxvQkFBb0JVLE1BQU1WLGtCQUFrQjtnQkFDNUNFLGdCQUFnQlEsTUFBTVIsY0FBYztnQkFDcENDLGdCQUFnQk8sTUFBTVAsY0FBYztnQkFDcENDLG1CQUFtQk0sTUFBTU4saUJBQWlCO2dCQUMxQ0gsaUJBQWlCUyxNQUFNVCxlQUFlO2dCQUN0Q1csV0FBV0MsS0FBS0MsR0FBRztZQUNyQjtZQUNBQyxhQUFhQyxPQUFPLENBQUNSLGFBQWFTLEtBQUtDLFNBQVMsQ0FBQ1A7UUFDbkQ7SUFDRixFQUFFLE9BQU81QyxPQUFPO1FBQ2RULFFBQVFTLEtBQUssQ0FBQyx5Q0FBeUNBO0lBQ3pEO0FBQ0Y7QUFFQSxNQUFNb0QsNEJBQTRCO0lBQ2hDLElBQUk7UUFDRixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsYUFBYUwsYUFBYU0sT0FBTyxDQUFDYjtZQUN4QyxJQUFJWSxZQUFZO2dCQUNkLE1BQU1FLFNBQVNMLEtBQUtNLEtBQUssQ0FBQ0g7Z0JBQzFCLDJDQUEyQztnQkFDM0MsSUFBSUUsT0FBT1YsU0FBUyxJQUFJLEtBQU1FLEdBQUcsS0FBS1EsT0FBT1YsU0FBUyxHQUFJLEtBQUssS0FBSyxLQUFLLE1BQU07b0JBQzdFLE9BQU9VO2dCQUNUO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBT3ZELE9BQU87UUFDZFQsUUFBUVMsS0FBSyxDQUFDLDJDQUEyQ0E7SUFDM0Q7SUFDQSxPQUFPO0FBQ1Q7QUFFQSxNQUFNeUQsaUJBQWlCLENBQUNkLE9BQXFCZTtJQUMzQyxPQUFRQSxPQUFPQyxJQUFJO1FBQ2pCLEtBQUs7WUFDSCxNQUFNQyxZQUFZO2dCQUFFLEdBQUdqQixNQUFNbEUsTUFBTTtnQkFBRSxHQUFHaUYsT0FBT0csT0FBTztZQUFDO1lBQ3ZELG1GQUFtRjtZQUNuRixJQUFJSCxPQUFPRyxPQUFPLENBQUNuRixPQUFPLElBQUlnRixPQUFPRyxPQUFPLENBQUNsRixPQUFPLEVBQUU7Z0JBQ3BELE9BQU87b0JBQUUsR0FBR2dFLEtBQUs7b0JBQUVsRSxRQUFRbUY7b0JBQVczQixvQkFBb0J6RCw0QkFBNEJvRjtnQkFBVztZQUNuRztZQUNBLE9BQU87Z0JBQUUsR0FBR2pCLEtBQUs7Z0JBQUVsRSxRQUFRbUY7WUFBVTtRQUN2QyxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHakIsS0FBSztnQkFBRWIsaUJBQWlCNEIsT0FBT0csT0FBTyxDQUFDQyxJQUFJLENBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVcsRUFBRUMsR0FBRyxDQUFDLENBQUNDLEtBQXFCQyxRQUFtQjt3QkFBQyxHQUFHRCxHQUFHO3dCQUFFRSxTQUFTRCxRQUFRO29CQUFDO1lBQUk7UUFDL00sS0FBSztZQUF3QjtnQkFDM0IsTUFBTUUsVUFBVTt1QkFBSTNCLE1BQU1iLGVBQWU7b0JBQUU0QixPQUFPRyxPQUFPO2lCQUFDLENBQUNDLElBQUksQ0FBQyxDQUFDQyxHQUFtQkMsSUFBc0JELEVBQUVFLFdBQVcsR0FBR0QsRUFBRUMsV0FBVyxFQUFFQyxHQUFHLENBQUMsQ0FBQ0MsS0FBcUJDLFFBQW1CO3dCQUFDLEdBQUdELEdBQUc7d0JBQUVFLFNBQVNELFFBQVE7b0JBQUM7Z0JBQ2pOLE9BQU87b0JBQUUsR0FBR3pCLEtBQUs7b0JBQUViLGlCQUFpQndDO2dCQUFRO1lBQzlDO1FBQ0EsS0FBSztZQUEyQjtnQkFDOUIsTUFBTUMsY0FBYzVCLE1BQU1iLGVBQWUsQ0FBQ29DLEdBQUcsQ0FBQyxDQUFDQyxNQUM3Q0EsSUFBSUssRUFBRSxLQUFLZCxPQUFPRyxPQUFPLENBQUNXLEVBQUUsR0FBR2QsT0FBT0csT0FBTyxHQUFHTSxLQUNoREwsSUFBSSxDQUFDLENBQUNDLEdBQW1CQyxJQUFzQkQsRUFBRUUsV0FBVyxHQUFHRCxFQUFFQyxXQUFXLEVBQUVDLEdBQUcsQ0FBQyxDQUFDQyxLQUFxQkMsUUFBbUI7d0JBQUMsR0FBR0QsR0FBRzt3QkFBRUUsU0FBU0QsUUFBUTtvQkFBQztnQkFDeEosT0FBTztvQkFBRSxHQUFHekIsS0FBSztvQkFBRWIsaUJBQWlCeUM7Z0JBQVk7WUFDbEQ7UUFDQSxLQUFLO1lBQTJCO2dCQUM5QixNQUFNRSxlQUFlOUIsTUFBTWIsZUFBZSxDQUFDNEMsTUFBTSxDQUFDLENBQUNQLE1BQXdCQSxJQUFJSyxFQUFFLEtBQUtkLE9BQU9HLE9BQU8sRUFBRUMsSUFBSSxDQUFDLENBQUNDLEdBQW1CQyxJQUFzQkQsRUFBRUUsV0FBVyxHQUFHRCxFQUFFQyxXQUFXLEVBQUVDLEdBQUcsQ0FBQyxDQUFDQyxLQUFxQkMsUUFBbUI7d0JBQUMsR0FBR0QsR0FBRzt3QkFBRUUsU0FBU0QsUUFBUTtvQkFBQztnQkFDNVAsT0FBTztvQkFBRSxHQUFHekIsS0FBSztvQkFBRWIsaUJBQWlCMkM7Z0JBQWE7WUFDbkQ7UUFDQSxLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHOUIsS0FBSztnQkFBRVosY0FBYztvQkFBQzJCLE9BQU9HLE9BQU87dUJBQUtsQixNQUFNWixZQUFZO2lCQUFDO1lBQUM7UUFDM0UsS0FBSztZQUNILE9BQU87Z0JBQUUsR0FBR1ksS0FBSztnQkFBRVosY0FBYyxFQUFFO1lBQUM7UUFDdEMsS0FBSztZQUNILE9BQU87Z0JBQUUsR0FBR1ksS0FBSztnQkFBRVgsYUFBYTtvQkFBRSxHQUFHVyxNQUFNWCxXQUFXO29CQUFFLEdBQUcwQixPQUFPRyxPQUFPO2dCQUFDO1lBQUU7UUFDOUUsS0FBSztZQUNILE9BQU87Z0JBQUUsR0FBR2xCLEtBQUs7Z0JBQUVWLG9CQUFvQnlCLE9BQU9HLE9BQU87WUFBQztRQUN4RCxLQUFLO1lBQTBCO2dCQUM3QixJQUFJbEIsTUFBTVYsa0JBQWtCLElBQUksR0FBRyxPQUFPVTtnQkFDMUMsNkRBQTZEO2dCQUM3RCxNQUFNZ0Msb0JBQW9CLENBQUMzRCxLQUFLQyxNQUFNLEtBQUssR0FBRSxJQUFLLE9BQU8sNkJBQTZCO2dCQUN0RixNQUFNMkQsV0FBV2pDLE1BQU1WLGtCQUFrQixHQUFJLEtBQUkwQyxpQkFBZ0I7Z0JBQ2pFLE9BQU87b0JBQUUsR0FBR2hDLEtBQUs7b0JBQUVWLG9CQUFvQjJDLFdBQVcsSUFBSUEsV0FBV2pDLE1BQU1WLGtCQUFrQjtnQkFBQztZQUM1RjtRQUNBLG9DQUFvQztRQUNwQyxLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHVSxLQUFLO2dCQUNSUixnQkFBZ0J1QixPQUFPRyxPQUFPLENBQUNuRixPQUFPLEtBQUttRyxZQUFZbkIsT0FBT0csT0FBTyxDQUFDbkYsT0FBTyxHQUFHaUUsTUFBTVIsY0FBYztnQkFDcEdDLGdCQUFnQnNCLE9BQU9HLE9BQU8sQ0FBQ2xGLE9BQU8sS0FBS2tHLFlBQVluQixPQUFPRyxPQUFPLENBQUNsRixPQUFPLEdBQUdnRSxNQUFNUCxjQUFjO2dCQUNwR0MsbUJBQW1CcUIsT0FBT0csT0FBTyxDQUFDekQsVUFBVSxLQUFLeUUsWUFBWW5CLE9BQU9HLE9BQU8sQ0FBQ3pELFVBQVUsR0FBR3VDLE1BQU1OLGlCQUFpQjtZQUNsSDtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdNLEtBQUs7Z0JBQ1JOLG1CQUFtQnFCLE9BQU9HLE9BQU87WUFDbkM7UUFDRixLQUFLO1lBQ0gsTUFBTWlCLGlCQUFpQjtnQkFBRSxHQUFHbkMsTUFBTWxFLE1BQU07WUFBQztZQUN6QyxPQUFPO2dCQUNMLEdBQUdvRCxtQkFBbUI7Z0JBQ3RCcEQsUUFBUXFHO2dCQUNSOUMsYUFBYTtvQkFBRSxHQUFHVyxNQUFNWCxXQUFXO2dCQUFDO2dCQUNwQ0Msb0JBQW9CekQsNEJBQTRCc0c7WUFDbEQ7UUFDRixLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHbkMsS0FBSztnQkFBRUwsZUFBZW9CLE9BQU9HLE9BQU87WUFBQztRQUNuRCxLQUFLO1lBQ0gsb0RBQW9EO1lBQ3BELE9BQU87Z0JBQ0wsR0FBR2xCLEtBQUs7Z0JBQ1JULGlCQUFpQjtZQUVuQjtRQUNGLEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUdTLEtBQUs7Z0JBQUVULGlCQUFpQjtZQUFVO1FBQ2hELEtBQUs7WUFDSCxPQUFPO2dCQUFFLEdBQUdTLEtBQUs7Z0JBQUVULGlCQUFpQjtZQUFVO1FBQ2hELEtBQUs7WUFDSCx3RUFBd0U7WUFDeEVLLDhCQUE4QndDLEtBQUs7WUFDbkMsT0FBTztnQkFDTCxHQUFHcEMsS0FBSztnQkFDUlQsaUJBQWlCO2dCQUNqQkosaUJBQWlCLEVBQUU7Z0JBQ25CQyxjQUFjLEVBQUU7WUFDbEI7UUFDRixLQUFLO1lBQ0gsT0FBTztnQkFBRSxHQUFHWSxLQUFLO2dCQUFFYixpQkFBaUI0QixPQUFPRyxPQUFPO1lBQUM7UUFDckQ7WUFDRSxPQUFPbEI7SUFDWDtBQUNGO0FBZ0JBLE1BQU1xQywrQkFBaUJ6SCxvREFBYUEsQ0FBaUNzSDtBQUVyRSwwQ0FBMEM7QUFFbkMsTUFBTUksa0JBQWtCO1FBQUMsRUFBRUMsUUFBUSxFQUEyQjs7SUFDbkUsdURBQXVEO0lBQ3ZELE1BQU1DLGtCQUFrQjtRQUN0QixNQUFNOUIsYUFBYUQ7UUFDbkIsSUFBSUMsWUFBWTtZQUNkLE9BQU87Z0JBQUUsR0FBR3hCLG1CQUFtQjtnQkFBRSxHQUFHd0IsVUFBVTtZQUFDO1FBQ2pEO1FBQ0EsT0FBT3hCO0lBQ1Q7SUFFQSxNQUFNLENBQUNjLE9BQU95QyxTQUFTLEdBQUczSCxpREFBVUEsQ0FBQ2dHLGdCQUFnQjBCO0lBQ3JELE1BQU0sRUFBRUUsS0FBSyxFQUFFLEdBQUduSCwwREFBUUE7SUFDMUIsTUFBTW9ILFdBQVcxSCw2Q0FBTUEsQ0FBMEI7SUFDakQsZ0VBQWdFO0lBRWhFLG9DQUFvQztJQUNwQyxNQUFNMkgsbUJBQW1CNUgsa0RBQVdBO3lEQUFDO1lBQ25DLElBQUk7Z0JBQ0YscURBQXFEO2dCQUNyRCxJQUFJLENBQUNnRixNQUFNbEUsTUFBTSxDQUFDQyxPQUFPLElBQUksQ0FBQ2lFLE1BQU1sRSxNQUFNLENBQUNFLE9BQU8sRUFBRTtvQkFDbER5RyxTQUFTO3dCQUFFekIsTUFBTTt3QkFBb0JFLFNBQVM7b0JBQUU7b0JBQ2hEO2dCQUNGO2dCQUNBLE1BQU14RSxRQUFRLE1BQU1SLHNCQUFzQjhELE1BQU1sRSxNQUFNO2dCQUN0RDJHLFNBQVM7b0JBQUV6QixNQUFNO29CQUFvQkUsU0FBU3hFO2dCQUFNO1lBQ3RELEVBQUUsT0FBT1csT0FBTztnQkFDZFQsUUFBUVMsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDakQ7UUFDRjt3REFBRztRQUFDMkMsTUFBTWxFLE1BQU07UUFBRTJHO0tBQVM7SUFFM0Isc0VBQXNFO0lBQ3RFMUgsZ0RBQVNBO3FDQUFDO1lBQ1IsZ0JBQWdCO1lBQ2hCNkg7WUFFQSxtREFBbUQ7WUFDbkQsTUFBTUMsMkJBQTJCQztzRUFBWTtvQkFDM0NMLFNBQVM7d0JBQUV6QixNQUFNO29CQUF5QjtnQkFDNUM7cUVBQUcsT0FBTyxrREFBa0Q7WUFFNUQ7NkNBQU87b0JBQ0wrQixjQUFjRjtnQkFDaEI7O1FBQ0Y7b0NBQUc7UUFBQ0Q7UUFBa0JIO0tBQVM7SUFFL0IsZ0JBQWdCO0lBQ2hCMUgsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSSxJQUE2QixFQUFFO2dCQUMvQjRILFNBQVNLLE9BQU8sR0FBRyxJQUFJQztZQUMzQjtRQUNGO29DQUFHLEVBQUU7SUFFTCxNQUFNQyxZQUFZbEksa0RBQVdBO2tEQUFDLENBQUNtSTtZQUM3QixJQUFJbkQsTUFBTVgsV0FBVyxDQUFDK0Qsa0JBQWtCLElBQUlULFNBQVNLLE9BQU8sRUFBRTtnQkFDNUQsSUFBSUs7Z0JBQ0osSUFBSUYsYUFBYSx5QkFBeUJuRCxNQUFNWCxXQUFXLENBQUNpRSxxQkFBcUIsRUFBRTtvQkFDakZELFlBQVlyRCxNQUFNWCxXQUFXLENBQUNrRSxtQkFBbUI7Z0JBQ25ELE9BQU8sSUFBSUosYUFBYSxnQkFBZ0JuRCxNQUFNWCxXQUFXLENBQUNtRSxZQUFZLEVBQUU7b0JBQ3RFSCxZQUFZckQsTUFBTVgsV0FBVyxDQUFDb0UsVUFBVTtnQkFDMUM7Z0JBRUEsSUFBSUosV0FBVztvQkFDYlYsU0FBU0ssT0FBTyxDQUFDVSxHQUFHLEdBQUdMO29CQUN2QlYsU0FBU0ssT0FBTyxDQUFDVyxXQUFXLEdBQUcsR0FBRyxxQkFBcUI7b0JBRXZELGlEQUFpRDtvQkFDakRoQixTQUFTSyxPQUFPLENBQUNZLElBQUksR0FBR0MsSUFBSTtrRUFBQzs0QkFDM0IsbURBQW1EOzRCQUNuREM7MEVBQVc7b0NBQ1QsSUFBSW5CLFNBQVNLLE9BQU8sRUFBRTt3Q0FDcEJMLFNBQVNLLE9BQU8sQ0FBQ2UsS0FBSzt3Q0FDdEJwQixTQUFTSyxPQUFPLENBQUNXLFdBQVcsR0FBRyxHQUFHLHNCQUFzQjtvQ0FDMUQ7Z0NBQ0Y7eUVBQUcsT0FBTyxZQUFZO3dCQUN4QjtpRUFBR0ssS0FBSztrRUFBQ0MsQ0FBQUEsTUFBT3JILFFBQVFTLEtBQUssQ0FBQyx3QkFBd0I0Rzs7Z0JBQ3hEO1lBQ0Y7UUFDRjtpREFBRztRQUFDakUsTUFBTVgsV0FBVztLQUFDO0lBRXRCLGlDQUFpQztJQUNqQyxNQUFNNkUsMkJBQTJCbEosa0RBQVdBO2lFQUFDLE9BQU9tSjtZQUNsRCxJQUFJO2dCQUNGLE1BQU1DLGdCQUFnQi9ELGFBQWFNLE9BQU8sQ0FBQztnQkFDM0MsTUFBTTBELGlCQUFpQmhFLGFBQWFNLE9BQU8sQ0FBQztnQkFFNUMsSUFBSSxDQUFDeUQsaUJBQWlCLENBQUNDLGdCQUFnQjtvQkFDckN6SCxRQUFRQyxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEsTUFBTVIsV0FBVyxNQUFNQyxNQUFNLCtCQUE2QyxPQUFkOEgsZUFBYyxpQkFBZTtvQkFDdkZFLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtvQkFDOUNDLE1BQU1qRSxLQUFLQyxTQUFTLENBQUM7d0JBQ25CaUUsU0FBU0o7d0JBQ1RLLE1BQU1QO3dCQUNOUSxZQUFZO29CQUNkO2dCQUNGO2dCQUVBLElBQUksQ0FBQ3RJLFNBQVNFLEVBQUUsRUFBRTtvQkFDaEJLLFFBQVFTLEtBQUssQ0FBQyx5Q0FBeUNoQixTQUFTdUksVUFBVTtnQkFDNUU7WUFDRixFQUFFLE9BQU92SCxPQUFPO2dCQUNkVCxRQUFRUyxLQUFLLENBQUMsd0NBQXdDQTtZQUN4RDtRQUNGO2dFQUFHLEVBQUU7SUFFTCwwREFBMEQ7SUFDMUR0QyxnREFBU0E7cUNBQUM7UUFDUiwwREFBMEQ7UUFDMUQseURBQXlEO1FBQ3pELDBEQUEwRDtRQUMxRCxrRkFBa0Y7UUFDbEYsNkJBQTZCO1FBQzdCLDJGQUEyRjtRQUMzRiw2Q0FBNkM7UUFDL0M7b0NBQUc7UUFBQ2lGLE1BQU1sRSxNQUFNLENBQUNDLE9BQU87UUFBRWlFLE1BQU1sRSxNQUFNLENBQUNFLE9BQU87S0FBQyxHQUFHLGtEQUFrRDtJQUVwRyxNQUFNNkksa0JBQWtCN0osa0RBQVdBO3dEQUFDLENBQUM4SjtZQUNuQyxJQUFJLENBQUNBLFVBQVUsQ0FBQ0MsTUFBTUMsT0FBTyxDQUFDRixTQUFTO1lBRXZDLG1FQUFtRTtZQUNuRSxNQUFNRyxlQUFlO21CQUFJSDthQUFPLENBQUMvQyxNQUFNOzZFQUFDLENBQUNyRixRQUFrQixDQUFDd0ksTUFBTXhJLFVBQVVBLFFBQVE7NEVBQUd5RSxJQUFJOzZFQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDOztZQUUxRyxNQUFNTSxVQUE0QnNELGFBQWExRCxHQUFHO3dFQUFDLENBQUM3RSxPQUFlK0U7b0JBQy9ELE1BQU0wRCxjQUFjbkYsTUFBTWIsZUFBZSxDQUFDaUcsSUFBSTs0RkFBQyxDQUFDQyxJQUFzQkEsRUFBRS9ELFdBQVcsS0FBSzVFOztvQkFDeEYsSUFBSXlJLGFBQWE7d0JBQ2YsMkRBQTJEO3dCQUMzRCxPQUFPOzRCQUFFLEdBQUdBLFdBQVc7NEJBQUV6RCxTQUFTRCxRQUFRO3dCQUFFO29CQUM5QztvQkFFQSxPQUFPO3dCQUNMSSxJQUFJeEcsZ0RBQU1BO3dCQUNWcUcsU0FBU0QsUUFBUTt3QkFDakI2RCxRQUFRO3dCQUNSQyxZQUFZO3dCQUNaQyxZQUFZeEYsTUFBTWxFLE1BQU0sQ0FBQzZDLE9BQU87d0JBQ2hDMkMsYUFBYTVFO29CQUNmO2dCQUNGOztZQUNGK0YsU0FBUztnQkFBRXpCLE1BQU07Z0JBQXlCRSxTQUFTUztZQUFRO1FBQzdEO3VEQUFHO1FBQUMzQixNQUFNYixlQUFlO1FBQUVhLE1BQU1sRSxNQUFNLENBQUM2QyxPQUFPO1FBQUU4RDtLQUFTO0lBRTFELDhEQUE4RDtJQUM5RDFILGdEQUFTQTtxQ0FBQztZQUNSLGtDQUFrQztZQUNsQyxJQUFJaUYsTUFBTVQsZUFBZSxLQUFLLGFBQWFTLE1BQU1iLGVBQWUsQ0FBQ3NHLE1BQU0sS0FBSyxLQUFLekYsTUFBTVYsa0JBQWtCLElBQUksR0FBRztnQkFDOUc7WUFDRjtZQUVBLHdFQUF3RTtZQUN4RSxNQUFNLEVBQUV4RCxNQUFNLEVBQUV3RCxrQkFBa0IsRUFBRUgsZUFBZSxFQUFFSyxjQUFjLEVBQUVDLGNBQWMsRUFBRSxHQUFHTztZQUN4RixNQUFNMEYscUJBQXFCO21CQUFJdkc7YUFBZ0IsQ0FBQ2dDLElBQUk7Z0VBQUMsQ0FBQ0MsR0FBbUJDLElBQXNCRCxFQUFFRSxXQUFXLEdBQUdELEVBQUVDLFdBQVc7O1lBRTVILCtEQUErRDtZQUMvRCxJQUFJcUUsd0JBQXdCbkc7WUFDNUIsSUFBSW9HLHdCQUF3Qm5HO1lBQzVCLElBQUlvRyxlQUFlO1lBRW5CakosUUFBUUMsR0FBRyxDQUFDLDJDQUE2RTZJLE9BQTVDcEcsbUJBQW1CZCxPQUFPLENBQUMsSUFBRyxnQkFBdURvSCxPQUF6Q0YsbUJBQW1CRCxNQUFNLEVBQUMsaUJBQXdDM0osT0FBekI4Six1QkFBc0IsS0FBa0IsT0FBZjlKLE9BQU9FLE9BQU87WUFFekssa0NBQWtDO1lBQ2xDLE1BQU04SixpQkFBaUJKLG1CQUFtQjNELE1BQU07NERBQUNQLENBQUFBO29CQUMvQyxNQUFNdUUsY0FBYyxLQUFNQyxHQUFHLENBQUMxRyxxQkFBcUJrQyxJQUFJRixXQUFXLElBQUloQyxxQkFBc0I7b0JBQzVGLE9BQU95RyxlQUFlakssT0FBT2dELGVBQWU7Z0JBQzlDOztZQUVBLElBQUlnSCxlQUFlTCxNQUFNLEdBQUcsR0FBRztnQkFDN0I3SSxRQUFRQyxHQUFHLENBQUMsc0NBQWdELE9BQXZCZixPQUFPZ0QsZUFBZSxFQUFDLFFBQU1nSCxlQUFldkUsR0FBRztpREFBQ0MsQ0FBQUEsTUFBTyxXQUEyQkEsT0FBaEJBLElBQUlFLE9BQU8sRUFBQyxNQUFlLE9BQVhGLElBQUk4RCxNQUFNLEVBQUM7O1lBQ3BJO1lBRUEsNERBQTREO1lBQzVELElBQUssSUFBSVcsSUFBSSxHQUFHQSxJQUFJUCxtQkFBbUJELE1BQU0sRUFBRVEsSUFBSztnQkFDbEQsTUFBTUMsWUFBWVIsa0JBQWtCLENBQUNPLEVBQUU7Z0JBQ3ZDLE1BQU1FLG1CQUFtQjlILEtBQUsySCxHQUFHLENBQUMxRyxxQkFBcUI0RyxVQUFVNUUsV0FBVyxJQUFJaEMscUJBQXFCO2dCQUVyRyxtRUFBbUU7Z0JBQ25FLElBQUk2RyxvQkFBb0JySyxPQUFPZ0QsZUFBZSxFQUFFO29CQUU5QyxJQUFJaEQsT0FBTzRDLFdBQVcsS0FBSyxjQUFjO3dCQUN2QyxpREFBaUQ7d0JBQ2pELElBQUl3SCxVQUFVWixNQUFNLEtBQUssUUFBUTs0QkFDL0IscUNBQXFDOzRCQUNyQyxNQUFNYyxjQUFjRixVQUFVVixVQUFVOzRCQUV4QyxJQUFJSSx5QkFBeUJRLGFBQWE7Z0NBQ3hDLE1BQU1DLHNCQUFzQkQsY0FBYzlHO2dDQUMxQyxNQUFNZ0gsYUFBNkI7b0NBQ2pDLEdBQUdKLFNBQVM7b0NBQ1paLFFBQVE7b0NBQ1JDLFlBQVlXLFVBQVVYLFVBQVUsR0FBRztvQ0FDbkNDLFlBQVkxSixPQUFPNkMsT0FBTyxHQUFHTixLQUFLa0ksR0FBRyxDQUFDekssT0FBTzhDLFVBQVUsRUFBRXNILFVBQVVYLFVBQVUsR0FBRztvQ0FDaEZpQixtQkFBbUJIO29DQUNuQkkscUJBQXFCTDtvQ0FDckJNLFlBQVlMO29DQUNaTSxZQUFZLENBQUNQO2dDQUNmO2dDQUNBM0QsU0FBUztvQ0FBRXpCLE1BQU07b0NBQTJCRSxTQUFTb0Y7Z0NBQVc7Z0NBQ2hFN0QsU0FBUztvQ0FBRXpCLE1BQU07b0NBQW1CRSxTQUFTO3dDQUFFbkYsU0FBUzRKLHdCQUF3QlU7d0NBQXFCckssU0FBUzRKLHdCQUF3QlE7b0NBQVk7Z0NBQUU7Z0NBQ3BKM0QsU0FBUztvQ0FDUHpCLE1BQU07b0NBQ05FLFNBQVM7d0NBQ1BXLElBQUl4RyxnREFBTUE7d0NBQ1Y2RSxXQUFXQyxLQUFLQyxHQUFHO3dDQUNuQndHLE1BQU0sR0FBcUI5SyxPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQWtCLE9BQWZELE9BQU9FLE9BQU87d0NBQ3pDRCxTQUFTRCxPQUFPQyxPQUFPO3dDQUN2QjhLLFdBQVc7d0NBQ1hDLGVBQWVUO3dDQUNmVSxVQUFVekg7d0NBQ1YwSCxjQUFjWjt3Q0FDZGEsUUFBUTNIO3dDQUNSNEgsZUFBZXBMLE9BQU9DLE9BQU8sSUFBSTt3Q0FDakNvTCxlQUFlckwsT0FBT0UsT0FBTyxJQUFJO29DQUNuQztnQ0FDRjtnQ0FDQVksUUFBUUMsR0FBRyxDQUFDLGtCQUE4Q3dKLE9BQTVCSCxVQUFVeEUsT0FBTyxFQUFDLFlBQTRDNUYsT0FBbEN1SyxvQkFBb0I3SCxPQUFPLENBQUMsSUFBRyxLQUF5QmMsT0FBdEJ4RCxPQUFPQyxPQUFPLEVBQUMsU0FBcUMsT0FBOUJ1RCxtQkFBbUJkLE9BQU8sQ0FBQztnQ0FDN0lrRSxNQUFNO29DQUFFMEUsT0FBTztvQ0FBZ0JDLGFBQWEsV0FBaUNoQixPQUF0QkgsVUFBVXhFLE9BQU8sRUFBQyxNQUFzQzVGLE9BQWxDdUssb0JBQW9CN0gsT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9DLE9BQU87b0NBQUl1TCxVQUFVO2dDQUFLO2dDQUNoSnBFLFVBQVU7Z0NBRVYscUNBQXFDO2dDQUNyQ2dCLHlCQUNFLHVDQUNBLHlCQUFpQyxPQUFsQmdDLFVBQVV4RSxPQUFPLEVBQUMsUUFDakMsd0JBQWdENUYsT0FBbEN1SyxvQkFBb0I3SCxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmMUMsT0FBT0MsT0FBTyxFQUFDLFFBQy9ELHdCQUE0QyxPQUE5QnVELG1CQUFtQmQsT0FBTyxDQUFDLElBQUcsUUFDNUMsdUJBQXVDMUMsT0FBMUJzSyxZQUFZNUgsT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9FLE9BQU8sRUFBQyxRQUNyRDtnQ0FHSDZKO2dDQUVBLDJEQUEyRDtnQ0FDM0RELHlCQUF5QlE7Z0NBQ3pCVCx5QkFBeUJVOzRCQUMzQjt3QkFDRjt3QkFFQSxnR0FBZ0c7d0JBQ2hHLE1BQU1rQixpQkFBaUJyQixVQUFVeEUsT0FBTzt3QkFDeEMsTUFBTThGLGNBQWM5QixtQkFBbUJOLElBQUk7cUVBQUM1RCxDQUFBQSxNQUFPQSxJQUFJRSxPQUFPLEtBQUs2RixpQkFBaUI7O3dCQUVwRixJQUFJQyxlQUFlQSxZQUFZbEMsTUFBTSxLQUFLLFVBQVVrQyxZQUFZaEIsaUJBQWlCLElBQUlnQixZQUFZZixtQkFBbUIsRUFBRTs0QkFDcEgsTUFBTWdCLHNCQUFzQkQsWUFBWWhCLGlCQUFpQjs0QkFDekQsTUFBTWtCLGtCQUFrQkQsc0JBQXNCbkk7NEJBQzlDLE1BQU1xSSxpQkFBaUJELGtCQUFrQkYsWUFBWWYsbUJBQW1COzRCQUV4RSxpRUFBaUU7NEJBQ2pFLE1BQU1tQix3QkFBd0J0SSxxQkFBcUIsSUFBSSxpQkFBa0J4RCxPQUFPaUQseUJBQXlCLEdBQUcsTUFBT08scUJBQXFCOzRCQUV4SSxNQUFNdUkscUJBQXFDO2dDQUN6QyxHQUFHTCxXQUFXO2dDQUNkbEMsUUFBUTtnQ0FDUmtCLG1CQUFtQnRFO2dDQUNuQnVFLHFCQUFxQnZFO2dDQUNyQnNELFlBQVkxSixPQUFPNkMsT0FBTyxHQUFHTixLQUFLa0ksR0FBRyxDQUFDekssT0FBTzhDLFVBQVUsRUFBRTRJLFlBQVlqQyxVQUFVO2dDQUMvRW1CLFlBQVksQ0FBQ2U7Z0NBQ2JkLFlBQVllOzRCQUNkOzRCQUNBakYsU0FBUztnQ0FBRXpCLE1BQU07Z0NBQTJCRSxTQUFTMkc7NEJBQW1COzRCQUN4RXBGLFNBQVM7Z0NBQUV6QixNQUFNO2dDQUFtQkUsU0FBUztvQ0FBRW5GLFNBQVM0Six3QkFBd0I4QjtvQ0FBcUJ6TCxTQUFTNEosd0JBQXdCOEI7Z0NBQWdCOzRCQUFFOzRCQUN4SmpGLFNBQVM7Z0NBQ1B6QixNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJeEcsZ0RBQU1BO29DQUNWNkUsV0FBV0MsS0FBS0MsR0FBRztvQ0FDbkJ3RyxNQUFNLEdBQXFCOUssT0FBbEJBLE9BQU9DLE9BQU8sRUFBQyxLQUFrQixPQUFmRCxPQUFPRSxPQUFPO29DQUN6Q0QsU0FBU0QsT0FBT0MsT0FBTztvQ0FDdkI4SyxXQUFXO29DQUNYQyxlQUFlVztvQ0FDZlYsVUFBVXpIO29DQUNWMEgsY0FBY1U7b0NBQ2RULFFBQVEzSDtvQ0FDUjRILGVBQWVwTCxPQUFPQyxPQUFPLElBQUk7b0NBQ2pDb0wsZUFBZXJMLE9BQU9FLE9BQU8sSUFBSTtvQ0FDakM4TCwyQkFBMkJIO29DQUMzQkksMkJBQTJCSDtnQ0FDN0I7NEJBQ0Y7NEJBQ0FoTCxRQUFRQyxHQUFHLENBQUMsbUJBQThDNEssT0FBM0JGLGlCQUFpQixHQUFFLFVBQTBDekwsT0FBbEMyTCxvQkFBb0JqSixPQUFPLENBQUMsSUFBRyxLQUErQm1KLE9BQTVCN0wsT0FBT0MsT0FBTyxFQUFDLGVBQXVDLE9BQTFCNEwsZUFBZW5KLE9BQU8sQ0FBQzs0QkFDL0lrRSxNQUFNO2dDQUFFMEUsT0FBTztnQ0FBaUJDLGFBQWEsV0FBMENNLE9BQS9CSixpQkFBaUIsR0FBRSxjQUFzQyxPQUExQkksZUFBZW5KLE9BQU8sQ0FBQztnQ0FBTThJLFVBQVU7NEJBQUs7NEJBQ25JcEUsVUFBVTs0QkFFVixzQ0FBc0M7NEJBQ3RDLE1BQU04RSxjQUFjTCxpQkFBaUIsSUFBSSxPQUFPQSxpQkFBaUIsSUFBSSxPQUFPOzRCQUM1RXpELHlCQUNFLHdDQUNBLHlCQUFrQyxPQUFuQnFELGlCQUFpQixHQUFFLFFBQ2xDLHdCQUFnRHpMLE9BQWxDMkwsb0JBQW9CakosT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9DLE9BQU8sRUFBQyxRQUMvRCx3QkFBNEMsT0FBOUJ1RCxtQkFBbUJkLE9BQU8sQ0FBQyxJQUFHLFFBQzVDLDJCQUErQzFDLE9BQTlCNEwsZ0JBQWdCbEosT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9FLE9BQU8sRUFBQyxRQUM5RCxHQUEyQjJMLE9BQXhCSyxhQUFZLGNBQXlDbE0sT0FBN0I2TCxlQUFlbkosT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9FLE9BQU8sRUFBQyxRQUN0RTs0QkFHSDZKOzRCQUVBLDJEQUEyRDs0QkFDM0RGLHlCQUF5QjhCOzRCQUN6QjdCLHlCQUF5QjhCO3dCQUMzQjtvQkFDRixPQUFPLElBQUk1TCxPQUFPNEMsV0FBVyxLQUFLLGtCQUFrQjt3QkFDbEQsd0VBQXdFO3dCQUN4RSxJQUFJd0gsVUFBVVosTUFBTSxLQUFLLFFBQVE7NEJBQy9CLHVFQUF1RTs0QkFDdkUsTUFBTTJDLHFCQUFxQi9CLFVBQVVWLFVBQVUsRUFBRSx5Q0FBeUM7NEJBRTFGLElBQUlJLHlCQUF5QnFDLG9CQUFvQjtnQ0FDL0MsK0NBQStDO2dDQUMvQywwRUFBMEU7Z0NBQzFFLE1BQU1DLHlCQUF5QnBLLFlBQVloQyxPQUFPRSxPQUFPLElBQUksVUFBVThCLFlBQVloQyxPQUFPbUQsbUJBQW1CLElBQUk7Z0NBQ2pILE1BQU1rSixxQkFBcUJGLHFCQUFxQkM7Z0NBRWhELHNDQUFzQztnQ0FDdEMsb0RBQW9EO2dDQUNwRCxNQUFNRSx5QkFBeUJ0SyxZQUFZaEMsT0FBT0MsT0FBTyxJQUFJLFNBQVMrQixZQUFZaEMsT0FBT21ELG1CQUFtQixJQUFJO2dDQUNoSCxNQUFNb0osZ0JBQWdCRixxQkFBcUJDO2dDQUUzQyx5REFBeUQ7Z0NBQ3pELE1BQU1FLFdBQVdwQyxVQUFVWCxVQUFVLEdBQUc7Z0NBQ3hDLE1BQU1nRCxXQUFXek0sT0FBTzZDLE9BQU8sR0FBR04sS0FBS2tJLEdBQUcsQ0FBQ3pLLE9BQU84QyxVQUFVLEVBQUUwSjtnQ0FFOUQsTUFBTWhDLGFBQTZCO29DQUNqQyxHQUFHSixTQUFTO29DQUNaWixRQUFRO29DQUNSQyxZQUFZK0M7b0NBQ1o5QyxZQUFZK0M7b0NBQ1ovQixtQkFBbUI2QjtvQ0FDbkI1QixxQkFBcUJ3QjtvQ0FDckJ2QixZQUFZMkI7b0NBQ1oxQixZQUFZLENBQUNzQjtnQ0FDZjtnQ0FFQXhGLFNBQVM7b0NBQUV6QixNQUFNO29DQUEyQkUsU0FBU29GO2dDQUFXO2dDQUNoRTdELFNBQVM7b0NBQUV6QixNQUFNO29DQUFtQkUsU0FBUzt3Q0FBRW5GLFNBQVM0Six3QkFBd0IwQzt3Q0FBZXJNLFNBQVM0Six3QkFBd0JxQztvQ0FBbUI7Z0NBQUU7Z0NBRXJKLDREQUE0RDtnQ0FDNUR4RixTQUFTO29DQUNQekIsTUFBTTtvQ0FDTkUsU0FBUzt3Q0FDUFcsSUFBSXhHLGdEQUFNQTt3Q0FDVjZFLFdBQVdDLEtBQUtDLEdBQUc7d0NBQ25Cd0csTUFBTSxHQUFxQjlLLE9BQWxCQSxPQUFPRSxPQUFPLEVBQUMsS0FBOEIsT0FBM0JGLE9BQU9tRCxtQkFBbUI7d0NBQ3JEbEQsU0FBU0QsT0FBT0UsT0FBTzt3Q0FDdkI2SyxXQUFXO3dDQUNYQyxlQUFlbUI7d0NBQ2ZsQixVQUFVbUI7d0NBQ1ZsQixjQUFjbUI7d0NBQ2RsQixRQUFRaUI7d0NBQ1JoQixlQUFlcEwsT0FBT0UsT0FBTyxJQUFJO3dDQUNqQ21MLGVBQWVyTCxPQUFPbUQsbUJBQW1CLElBQUk7b0NBQy9DO2dDQUNGO2dDQUVBd0QsU0FBUztvQ0FDUHpCLE1BQU07b0NBQ05FLFNBQVM7d0NBQ1BXLElBQUl4RyxnREFBTUE7d0NBQ1Y2RSxXQUFXQyxLQUFLQyxHQUFHO3dDQUNuQndHLE1BQU0sR0FBcUI5SyxPQUFsQkEsT0FBT0MsT0FBTyxFQUFDLEtBQThCLE9BQTNCRCxPQUFPbUQsbUJBQW1CO3dDQUNyRGxELFNBQVNELE9BQU9DLE9BQU87d0NBQ3ZCOEssV0FBVzt3Q0FDWEMsZUFBZXVCO3dDQUNmdEIsVUFBVXFCO3dDQUNWcEIsY0FBY21CO3dDQUNkbEIsUUFBUW1CO3dDQUNSbEIsZUFBZXBMLE9BQU9DLE9BQU8sSUFBSTt3Q0FDakNvTCxlQUFlckwsT0FBT21ELG1CQUFtQixJQUFJO29DQUMvQztnQ0FDRjtnQ0FFQXJDLFFBQVFDLEdBQUcsQ0FBQyw2QkFBaUVvTCxPQUFwQy9CLFVBQVV4RSxPQUFPLEVBQUMsb0JBQXdDNUYsT0FBdEJtTSxvQkFBbUIsS0FBdUJFLE9BQXBCck0sT0FBT0UsT0FBTyxFQUFDLE9BQXNDRixPQUFqQ3FNLG1CQUFtQjNKLE9BQU8sQ0FBQyxJQUFHLEtBQWtENkosT0FBL0N2TSxPQUFPbUQsbUJBQW1CLEVBQUMsc0JBQWdEbkQsT0FBNUJ1TSxjQUFjN0osT0FBTyxDQUFDLElBQUcsS0FBOEIwSCxPQUEzQnBLLE9BQU9DLE9BQU8sRUFBQyxjQUFzQ3VNLE9BQTFCcEMsVUFBVVgsVUFBVSxFQUFDLE9BQWMsT0FBVCtDO2dDQUN4UjVGLE1BQU07b0NBQUUwRSxPQUFPO29DQUE2QkMsYUFBYSxXQUFpQ2dCLE9BQXRCbkMsVUFBVXhFLE9BQU8sRUFBQyxNQUFnQzVGLE9BQTVCdU0sY0FBYzdKLE9BQU8sQ0FBQyxJQUFHLEtBQXlCMUMsT0FBdEJBLE9BQU9DLE9BQU8sRUFBQyxTQUFrQyxPQUEzQkQsT0FBT21ELG1CQUFtQjtvQ0FBSXFJLFVBQVU7Z0NBQUs7Z0NBQ3pMcEUsVUFBVTtnQ0FFVixnREFBZ0Q7Z0NBQ2hEZ0IseUJBQ0UseURBQ0EseUJBQWlDLE9BQWxCZ0MsVUFBVXhFLE9BQU8sRUFBQyxRQUNqQyw2QkFBb0Q1RixPQUFqQ21NLG1CQUFtQnpKLE9BQU8sQ0FBQyxJQUFHLEtBQXVCMkosT0FBcEJyTSxPQUFPRSxPQUFPLEVBQUMsT0FBc0NGLE9BQWpDcU0sbUJBQW1CM0osT0FBTyxDQUFDLElBQUcsS0FBOEIsT0FBM0IxQyxPQUFPbUQsbUJBQW1CLEVBQUMsUUFDcEksK0JBQWlEbkQsT0FBNUJ1TSxjQUFjN0osT0FBTyxDQUFDLElBQUcsS0FBa0IsT0FBZjFDLE9BQU9DLE9BQU8sRUFBQyxRQUNoRSx1QkFBdUN1TSxPQUExQnBDLFVBQVVYLFVBQVUsRUFBQyxPQUFjLE9BQVQrQyxVQUFTLFFBQy9DO2dDQUdIekM7Z0NBRUEsMkRBQTJEO2dDQUMzREQseUJBQXlCcUM7Z0NBQ3pCdEMseUJBQXlCMEM7NEJBQzNCO3dCQUNGO3dCQUVBLGdHQUFnRzt3QkFDaEcsTUFBTWQsaUJBQWlCckIsVUFBVXhFLE9BQU87d0JBQ3hDLE1BQU04RixjQUFjOUIsbUJBQW1CTixJQUFJO3FFQUFDNUQsQ0FBQUEsTUFBT0EsSUFBSUUsT0FBTyxLQUFLNkYsaUJBQWlCOzt3QkFFcEYsSUFBSUMsZUFBZUEsWUFBWWxDLE1BQU0sS0FBSyxVQUFVa0MsWUFBWWhCLGlCQUFpQixJQUFJZ0IsWUFBWWYsbUJBQW1CLEVBQUU7NEJBQ3BILDRGQUE0Rjs0QkFDNUYsTUFBTWdCLHNCQUFzQkQsWUFBWWhCLGlCQUFpQjs0QkFFekQsK0NBQStDOzRCQUMvQyxNQUFNNEIseUJBQXlCdEssWUFBWWhDLE9BQU9DLE9BQU8sSUFBSSxTQUFTK0IsWUFBWWhDLE9BQU9tRCxtQkFBbUIsSUFBSTs0QkFDaEgsTUFBTXVKLHVCQUF1QmYsc0JBQXNCVzs0QkFFbkQsc0NBQXNDOzRCQUN0QyxNQUFNRix5QkFBeUJwSyxZQUFZaEMsT0FBT0UsT0FBTyxJQUFJLFVBQVU4QixZQUFZaEMsT0FBT21ELG1CQUFtQixJQUFJOzRCQUNqSCxNQUFNd0osb0JBQW9CRCx1QkFBdUJOOzRCQUVqRCx1Q0FBdUM7NEJBQ3ZDLE1BQU1RLDBCQUEwQkQsb0JBQW9CakIsWUFBWWYsbUJBQW1COzRCQUVuRixpRUFBaUU7NEJBQ2pFLE1BQU1tQix3QkFBd0JRLHlCQUF5QixJQUFJLDBCQUEyQnRNLE9BQU9pRCx5QkFBeUIsR0FBRyxNQUFPcUoseUJBQXlCOzRCQUV6Siw2REFBNkQ7NEJBQzdELE1BQU1QLHFCQUFxQztnQ0FDekMsR0FBR0wsV0FBVztnQ0FDZGxDLFFBQVE7Z0NBQ1Isa0RBQWtEO2dDQUNsRGtCLG1CQUFtQnRFO2dDQUNuQnVFLHFCQUFxQnZFO2dDQUNyQnNELFlBQVkxSixPQUFPNkMsT0FBTyxHQUFHTixLQUFLa0ksR0FBRyxDQUFDekssT0FBTzhDLFVBQVUsRUFBRTRJLFlBQVlqQyxVQUFVO2dDQUMvRW1CLFlBQVk7Z0NBQ1pDLFlBQVk7NEJBQ2Q7NEJBRUFsRSxTQUFTO2dDQUFFekIsTUFBTTtnQ0FBMkJFLFNBQVMyRzs0QkFBbUI7NEJBQ3hFcEYsU0FBUztnQ0FBRXpCLE1BQU07Z0NBQW1CRSxTQUFTO29DQUFFbkYsU0FBUzRKLHdCQUF3QjhCO29DQUFxQnpMLFNBQVM0Six3QkFBd0I2QztnQ0FBa0I7NEJBQUU7NEJBRTFKLGdFQUFnRTs0QkFDaEVoRyxTQUFTO2dDQUNQekIsTUFBTTtnQ0FDTkUsU0FBUztvQ0FDUFcsSUFBSXhHLGdEQUFNQTtvQ0FDVjZFLFdBQVdDLEtBQUtDLEdBQUc7b0NBQ25Cd0csTUFBTSxHQUFxQjlLLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBOEIsT0FBM0JELE9BQU9tRCxtQkFBbUI7b0NBQ3JEbEQsU0FBU0QsT0FBT0MsT0FBTztvQ0FDdkI4SyxXQUFXO29DQUNYQyxlQUFlVztvQ0FDZlYsVUFBVXFCO29DQUNWcEIsY0FBY3dCO29DQUNkdkIsUUFBUW1CO29DQUNSbEIsZUFBZXBMLE9BQU9DLE9BQU8sSUFBSTtvQ0FDakNvTCxlQUFlckwsT0FBT21ELG1CQUFtQixJQUFJO2dDQUMvQzs0QkFDRjs0QkFFQXdELFNBQVM7Z0NBQ1B6QixNQUFNO2dDQUNORSxTQUFTO29DQUNQVyxJQUFJeEcsZ0RBQU1BO29DQUNWNkUsV0FBV0MsS0FBS0MsR0FBRztvQ0FDbkJ3RyxNQUFNLEdBQXFCOUssT0FBbEJBLE9BQU9FLE9BQU8sRUFBQyxLQUE4QixPQUEzQkYsT0FBT21ELG1CQUFtQjtvQ0FDckRsRCxTQUFTRCxPQUFPRSxPQUFPO29DQUN2QjZLLFdBQVc7b0NBQ1hDLGVBQWUyQjtvQ0FDZjFCLFVBQVVtQjtvQ0FDVmxCLGNBQWN3QjtvQ0FDZHZCLFFBQVFpQjtvQ0FDUmhCLGVBQWVwTCxPQUFPRSxPQUFPLElBQUk7b0NBQ2pDbUwsZUFBZXJMLE9BQU9tRCxtQkFBbUIsSUFBSTtvQ0FDN0M2SSwyQkFBMkJZO29DQUMzQlgsMkJBQTJCSDtnQ0FDN0I7NEJBQ0Y7NEJBRUFoTCxRQUFRQyxHQUFHLENBQUMsOEJBQW1FNEssT0FBckNGLGlCQUFpQixHQUFFLG9CQUFvRHpMLE9BQWxDMkwsb0JBQW9CakosT0FBTyxDQUFDLElBQUcsS0FBdUJnSyxPQUFwQjFNLE9BQU9DLE9BQU8sRUFBQyxPQUF3Q0QsT0FBbkMwTSxxQkFBcUJoSyxPQUFPLENBQUMsSUFBRyxLQUFrRGlLLE9BQS9DM00sT0FBT21ELG1CQUFtQixFQUFDLHNCQUFvRG5ELE9BQWhDMk0sa0JBQWtCakssT0FBTyxDQUFDLElBQUcsS0FBK0JrSyxPQUE1QjVNLE9BQU9FLE9BQU8sRUFBQyxlQUFtREYsT0FBdEM0TSx3QkFBd0JsSyxPQUFPLENBQUMsSUFBRyxLQUE4QmdKLE9BQTNCMUwsT0FBT0UsT0FBTyxFQUFDLGNBQW1DLE9BQXZCd0wsWUFBWWpDLFVBQVUsRUFBQzs0QkFDM1c3QyxNQUFNO2dDQUFFMEUsT0FBTztnQ0FBOEJDLGFBQWEsV0FBeUNxQixPQUE5Qm5CLGlCQUFpQixHQUFFLGFBQWlEekwsT0FBdEM0TSx3QkFBd0JsSyxPQUFPLENBQUMsSUFBRyxLQUF5QjFDLE9BQXRCQSxPQUFPRSxPQUFPLEVBQUMsU0FBa0MsT0FBM0JGLE9BQU9tRCxtQkFBbUI7Z0NBQUlxSSxVQUFVOzRCQUFLOzRCQUM1TXBFLFVBQVU7NEJBRVYsaURBQWlEOzRCQUNqRCxNQUFNOEUsY0FBY1UsMEJBQTBCLElBQUksT0FBT0EsMEJBQTBCLElBQUksT0FBTzs0QkFDOUZ4RSx5QkFDRSwwREFDQSx5QkFBa0MsT0FBbkJxRCxpQkFBaUIsR0FBRSxRQUNsQyw2QkFBcUR6TCxPQUFsQzJMLG9CQUFvQmpKLE9BQU8sQ0FBQyxJQUFHLEtBQXVCZ0ssT0FBcEIxTSxPQUFPQyxPQUFPLEVBQUMsT0FBd0NELE9BQW5DME0scUJBQXFCaEssT0FBTyxDQUFDLElBQUcsS0FBOEIsT0FBM0IxQyxPQUFPbUQsbUJBQW1CLEVBQUMsUUFDdkksK0JBQXFEbkQsT0FBaEMyTSxrQkFBa0JqSyxPQUFPLENBQUMsSUFBRyxLQUFrQixPQUFmMUMsT0FBT0UsT0FBTyxFQUFDLFFBQ3BFLEdBQTBCME0sT0FBdkJWLGFBQVksYUFBaURsTSxPQUF0QzRNLHdCQUF3QmxLLE9BQU8sQ0FBQyxJQUFHLEtBQWtCLE9BQWYxQyxPQUFPRSxPQUFPLEVBQUMsUUFDL0UsdUJBQW9DLE9BQXZCd0wsWUFBWWpDLFVBQVUsRUFBQyxvQkFDbkM7NEJBR0hNOzRCQUVBLDJEQUEyRDs0QkFDM0RGLHlCQUF5QjhCOzRCQUN6QjdCLHlCQUF5QjZDO3dCQUMzQjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsSUFBSTVDLGVBQWUsR0FBRztnQkFDcEJqSixRQUFRQyxHQUFHLENBQUMsZ0NBQThEeUMsT0FBeEN1RyxjQUFhLDZCQUF5RCxPQUE5QnZHLG1CQUFtQmQsT0FBTyxDQUFDO1lBQ3ZHO1FBRUY7b0NBQUc7UUFBQ3dCLE1BQU1ULGVBQWU7UUFBRVMsTUFBTVYsa0JBQWtCO1FBQUVVLE1BQU1iLGVBQWU7UUFBRWEsTUFBTWxFLE1BQU07UUFBRWtFLE1BQU1SLGNBQWM7UUFBRVEsTUFBTVAsY0FBYztRQUFFTyxNQUFNTixpQkFBaUI7UUFBRStDO1FBQVVDO1FBQU9RO1FBQVdnQjtLQUF5QjtJQUdwTixNQUFNeUUsbUJBQW1CM04sa0RBQVdBO3lEQUFDO1lBQ25DLElBQUksQ0FBQ2dGLE1BQU1iLGVBQWUsSUFBSSxDQUFDNEYsTUFBTUMsT0FBTyxDQUFDaEYsTUFBTWIsZUFBZSxHQUFHO2dCQUNuRSxPQUFPLEVBQUU7WUFDWDtZQUVBLE9BQU9hLE1BQU1iLGVBQWUsQ0FBQ29DLEdBQUc7aUVBQUMsQ0FBQ0M7b0JBQ2hDLE1BQU1vSCxlQUFlNUksTUFBTVYsa0JBQWtCLElBQUk7b0JBQ2pELE1BQU1nQyxjQUFjRSxJQUFJRixXQUFXLElBQUk7b0JBQ3ZDLE1BQU11SCx5QkFBeUJELGdCQUFnQnRILGNBQzNDLENBQUMsZUFBZ0JBLGNBQWUsS0FBSyxNQUNyQztvQkFFSixJQUFJd0g7b0JBQ0osSUFBSUM7b0JBRUosSUFBSXZILElBQUk4RCxNQUFNLEtBQUssVUFBVTlELElBQUlnRixpQkFBaUIsSUFBSWhGLElBQUlpRixtQkFBbUIsRUFBRTt3QkFDN0UsTUFBTXVDLGlDQUFpQyxlQUFnQnhILElBQUlnRixpQkFBaUIsR0FBSWhGLElBQUlpRixtQkFBbUI7d0JBQ3ZHc0MsZ0JBQWdCLGlDQUFrQy9JLE1BQU1sRSxNQUFNLENBQUNrRCx5QkFBeUIsR0FBSTt3QkFDNUYsSUFBSTRKLGVBQWUsR0FBRzs0QkFDcEJFLGdCQUFnQixpQ0FBa0M5SSxNQUFNbEUsTUFBTSxDQUFDaUQseUJBQXlCLEdBQUcsTUFBTzZKO3dCQUNwRztvQkFDRjtvQkFFQSxPQUFPO3dCQUNMLEdBQUdwSCxHQUFHO3dCQUNOb0g7d0JBQ0FLLGlCQUFpQjNILGNBQWNzSDt3QkFDL0JNLHdCQUF3Qk4sZUFBZSxJQUFJLENBQUV0SCxjQUFjc0gsWUFBVyxJQUFLQSxlQUFnQixNQUFNO3dCQUNqR08sd0JBQXdCLE1BQU9yTixNQUFNLENBQUNpRCx5QkFBeUIsR0FBRyxNQUFPeUMsSUFBSWdFLFVBQVUsR0FBSWxFLENBQUFBLGVBQWU7d0JBQzFHOEgsd0JBQXdCLE1BQU90TixNQUFNLENBQUNrRCx5QkFBeUIsR0FBRyxNQUFPd0MsSUFBSWdFLFVBQVU7d0JBQ3ZGcUQ7d0JBQ0FDO3dCQUNBQztvQkFDRjtnQkFDRjtnRUFBRzVILElBQUk7aUVBQUMsQ0FBQ0MsR0FBb0JDLElBQXVCQSxFQUFFQyxXQUFXLEdBQUdGLEVBQUVFLFdBQVc7O1FBQ25GO3dEQUFHO1FBQUN0QixNQUFNYixlQUFlO1FBQUVhLE1BQU1WLGtCQUFrQjtRQUFFVSxNQUFNbEUsTUFBTSxDQUFDaUQseUJBQXlCO1FBQUVpQixNQUFNbEUsTUFBTSxDQUFDa0QseUJBQXlCO1FBQUVnQixNQUFNbEUsTUFBTSxDQUFDNkMsT0FBTztRQUFFcUIsTUFBTWxFLE1BQU0sQ0FBQzhDLFVBQVU7S0FBQztJQUduTCxnQ0FBZ0M7SUFDaEMsTUFBTXlLLHNCQUFzQnJPLGtEQUFXQTs0REFBQyxPQUFPYztZQUM3QyxJQUFJO29CQWtCS087Z0JBakJQLE1BQU1pTixhQUFhO29CQUNqQkMsTUFBTSxHQUFxQnpOLE9BQWxCQSxPQUFPQyxPQUFPLEVBQUMsS0FBcUJELE9BQWxCQSxPQUFPRSxPQUFPLEVBQUMsS0FBc0IsT0FBbkJGLE9BQU80QyxXQUFXO29CQUMvREEsYUFBYTVDLE9BQU80QyxXQUFXO29CQUMvQjNDLFNBQVNELE9BQU9DLE9BQU87b0JBQ3ZCQyxTQUFTRixPQUFPRSxPQUFPO29CQUN2QjJDLFNBQVM3QyxPQUFPNkMsT0FBTztvQkFDdkJDLFlBQVk5QyxPQUFPOEMsVUFBVTtvQkFDN0JDLFdBQVcvQyxPQUFPK0MsU0FBUztvQkFDM0JDLGlCQUFpQmhELE9BQU9nRCxlQUFlO29CQUN2Q0MsMkJBQTJCakQsT0FBT2lELHlCQUF5QjtvQkFDM0RDLDJCQUEyQmxELE9BQU9rRCx5QkFBeUI7b0JBQzNEQyxxQkFBcUJuRCxPQUFPbUQsbUJBQW1CO29CQUMvQ3VLLGNBQWN4SixNQUFNYixlQUFlLENBQUNvQyxHQUFHOzRFQUFDQyxDQUFBQSxNQUFPQSxJQUFJRixXQUFXOztnQkFDaEU7Z0JBRUEsTUFBTWpGLFdBQVcsTUFBTWIsZ0RBQVVBLENBQUNpTyxVQUFVLENBQUNIO2dCQUM3QzFNLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJSO2dCQUMxQyxPQUFPQSxFQUFBQSxtQkFBQUEsU0FBU1AsTUFBTSxjQUFmTyx1Q0FBQUEsaUJBQWlCd0YsRUFBRSxLQUFJO1lBQ2hDLEVBQUUsT0FBT3hFLE9BQU87Z0JBQ2RULFFBQVFTLEtBQUssQ0FBQyx1Q0FBdUNBO2dCQUNyRHFGLE1BQU07b0JBQ0owRSxPQUFPO29CQUNQQyxhQUFhO29CQUNicUMsU0FBUztvQkFDVHBDLFVBQVU7Z0JBQ1o7Z0JBQ0EsT0FBTztZQUNUO1FBQ0Y7MkRBQUc7UUFBQ3RILE1BQU1iLGVBQWU7UUFBRXVEO0tBQU07SUFFakMsTUFBTWlILGtCQUFrQjNPLGtEQUFXQTt3REFBQyxPQUFPNE87WUFDekMsSUFBSTtnQkFDRixNQUFNdk4sV0FBVyxNQUFNYixnREFBVUEsQ0FBQ3FPLFFBQVEsQ0FBQ0Q7Z0JBQzNDaE4sUUFBUUMsR0FBRyxDQUFDLDZCQUE2QlI7Z0JBQ3pDcUcsTUFBTTtvQkFDSjBFLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFVBQVU7Z0JBQ1o7Z0JBQ0EsT0FBTztZQUNULEVBQUUsT0FBT2pLLE9BQU87Z0JBQ2RULFFBQVFTLEtBQUssQ0FBQyxxQ0FBcUNBO2dCQUNuRHFGLE1BQU07b0JBQ0owRSxPQUFPO29CQUNQQyxhQUFhO29CQUNicUMsU0FBUztvQkFDVHBDLFVBQVU7Z0JBQ1o7Z0JBQ0EsT0FBTztZQUNUO1FBQ0Y7dURBQUc7UUFBQzVFO0tBQU07SUFFVixNQUFNb0gsaUJBQWlCOU8sa0RBQVdBO3VEQUFDLE9BQU80TztZQUN4QyxJQUFJO2dCQUNGLE1BQU12TixXQUFXLE1BQU1iLGdEQUFVQSxDQUFDdU8sT0FBTyxDQUFDSDtnQkFDMUNoTixRQUFRQyxHQUFHLENBQUMsNkJBQTZCUjtnQkFDekNxRyxNQUFNO29CQUNKMEUsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsVUFBVTtnQkFDWjtnQkFDQSxPQUFPO1lBQ1QsRUFBRSxPQUFPakssT0FBTztnQkFDZFQsUUFBUVMsS0FBSyxDQUFDLG9DQUFvQ0E7Z0JBQ2xEcUYsTUFBTTtvQkFDSjBFLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JxQyxTQUFTO29CQUNUcEMsVUFBVTtnQkFDWjtnQkFDQSxPQUFPO1lBQ1Q7UUFDRjtzREFBRztRQUFDNUU7S0FBTTtJQUVWLE1BQU1zSCxxQkFBcUJoUCxrREFBV0E7MkRBQUM7WUFDckMsTUFBTWlQLFNBQVNDLHVCQUErQjtZQUM5QyxJQUFJLENBQUNELFFBQVE7Z0JBQ1hyTixRQUFRUyxLQUFLLENBQUM7Z0JBQ2RvRixTQUFTO29CQUFFekIsTUFBTTtvQkFBc0JFLFNBQVM7Z0JBQVU7Z0JBQzFEO1lBQ0Y7WUFDQSxJQUFJO2dCQUNGLE1BQU1tSixpQkFBaUIsTUFBTS9OLE1BQU0sR0FBVSxPQUFQMk4sUUFBTztnQkFDN0MsSUFBSSxDQUFDSSxlQUFlOU4sRUFBRSxFQUFFO29CQUN0QiwyREFBMkQ7b0JBQzNESyxRQUFRUyxLQUFLLENBQUMsNENBQXFFZ04sT0FBekJBLGVBQWUvRSxNQUFNLEVBQUMsS0FBNkIsT0FBMUIrRSxlQUFlekYsVUFBVTtvQkFDNUcsTUFBTTBGLGVBQWUsTUFBTUQsZUFBZTNGLElBQUksR0FBR1YsS0FBSzsyRUFBQyxJQUFNOztvQkFDN0RwSCxRQUFRUyxLQUFLLENBQUMsdUNBQXVDaU47Z0JBQ3ZEO2dCQUNBN0gsU0FBUztvQkFBRXpCLE1BQU07b0JBQXNCRSxTQUFTbUosZUFBZTlOLEVBQUUsR0FBRyxXQUFXO2dCQUFVO1lBQzNGLEVBQUUsT0FBT2MsT0FBWTtnQkFDbkJvRixTQUFTO29CQUFFekIsTUFBTTtvQkFBc0JFLFNBQVM7Z0JBQVU7Z0JBQzFEdEUsUUFBUVMsS0FBSyxDQUFDLHFEQUFxREE7Z0JBQ25FLElBQUlBLE1BQU1rTixLQUFLLEVBQUU7b0JBQ2YzTixRQUFRUyxLQUFLLENBQUMsc0JBQXNCQSxNQUFNa04sS0FBSztnQkFDakQ7Z0JBQ0EsbUVBQW1FO2dCQUNuRTNOLFFBQVFTLEtBQUssQ0FBQywrQkFBK0IsR0FBVSxPQUFQNE0sUUFBTztZQUN6RDtRQUNGOzBEQUFHO1FBQUN4SDtLQUFTO0lBRWIsa0RBQWtEO0lBQ2xEMUgsZ0RBQVNBO3FDQUFDO1lBQ1Isd0NBQXdDO1lBQ3hDaVA7UUFDRjtvQ0FBRztRQUFDQTtLQUFtQjtJQUV2QixpREFBaUQ7SUFDakRqUCxnREFBU0E7cUNBQUM7WUFDUmdGLHdCQUF3QkM7UUFDMUI7b0NBQUc7UUFBQ0E7S0FBTTtJQUdWLDREQUE0RDtJQUM1RGpGLGdEQUFTQTtxQ0FBQztZQUNSLElBQUlpRixNQUFNVCxlQUFlLEtBQUssYUFBYTtnQkFDekMzQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osb0RBQW9EO2dCQUNwRDRGLFNBQVM7b0JBQUV6QixNQUFNO2dCQUF5QjtnQkFDMUNwRSxRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGO29DQUFHO1FBQUNtRCxNQUFNVCxlQUFlO1FBQUVrRDtLQUFTO0lBRXBDLHFEQUFxRDtJQUNyRDFILGdEQUFTQTtxQ0FBQztZQUNSLE1BQU15UCxpQkFBaUIvTyxnRUFBY0EsQ0FBQ2dQLFdBQVc7WUFFakQsdURBQXVEO1lBQ3ZELElBQUl6SyxNQUFNVCxlQUFlLEtBQUssZUFBZSxDQUFDaUwsZUFBZUUsbUJBQW1CLElBQUk7Z0JBQ2xGLG9EQUFvRDtnQkFDcEQsSUFBSTFLLE1BQU1sRSxNQUFNLENBQUNDLE9BQU8sSUFBSWlFLE1BQU1sRSxNQUFNLENBQUNFLE9BQU8sRUFBRTtvQkFDaER3TyxlQUFlRyw0QkFBNEIsQ0FBQzNLLE1BQU1sRSxNQUFNLEVBQ3JEK0gsSUFBSTtxREFBQyxDQUFDK0c7NEJBQ0xKLGVBQWVLLGlCQUFpQixDQUFDRDs0QkFDakNoTyxRQUFRQyxHQUFHLENBQUMsMkJBQTJCK047d0JBQ3pDO29EQUNDNUcsS0FBSztxREFBQyxDQUFDM0c7NEJBQ05ULFFBQVFTLEtBQUssQ0FBQyxvQ0FBb0NBO3dCQUNwRDs7Z0JBQ0o7WUFDRjtRQUNGO29DQUFHO1FBQUMyQyxNQUFNVCxlQUFlO1FBQUVTLE1BQU1sRSxNQUFNLENBQUNDLE9BQU87UUFBRWlFLE1BQU1sRSxNQUFNLENBQUNFLE9BQU87S0FBQztJQUV0RSw4Q0FBOEM7SUFDOUNqQixnREFBU0E7cUNBQUM7WUFDUixNQUFNK1AsaUJBQWlCcFAsZ0VBQWNBLENBQUMrTyxXQUFXO1lBQ2pELE1BQU1NLGtCQUFrQnBQLGlFQUFlQSxDQUFDOE8sV0FBVztZQUNuRCxNQUFNTyxnQkFBZ0JwUCwrREFBYUEsQ0FBQzZPLFdBQVc7WUFDL0MsTUFBTUQsaUJBQWlCL08sZ0VBQWNBLENBQUNnUCxXQUFXO1lBRWpELGlDQUFpQztZQUNqQyxNQUFNUSxxQkFBcUJILGVBQWVJLFdBQVc7Z0VBQUMsQ0FBQ0MsVUFBVUM7b0JBQy9EeE8sUUFBUUMsR0FBRyxDQUFDLHdDQUE4RCxPQUFoQ3NPLFdBQVcsV0FBVztvQkFFaEUsNEVBQTRFO29CQUM1RSxJQUFJLENBQUNDLFdBQVc7d0JBQ2QsSUFBSSxDQUFDRCxVQUFVOzRCQUNiekksTUFBTTtnQ0FDSjBFLE9BQU87Z0NBQ1BDLGFBQWE7Z0NBQ2JxQyxTQUFTO2dDQUNUcEMsVUFBVTs0QkFDWjt3QkFDRixPQUFPOzRCQUNMNUUsTUFBTTtnQ0FDSjBFLE9BQU87Z0NBQ1BDLGFBQWE7Z0NBQ2JDLFVBQVU7NEJBQ1o7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsMkJBQTJCO1lBQzNCLE1BQU0rRCxvQkFBb0JMLGNBQWNFLFdBQVc7K0RBQUMsQ0FBQ0k7b0JBQ25ELE1BQU1DLFNBQVNELE9BQU9FLGNBQWMsR0FBRyxPQUFPO29CQUM5QyxJQUFJRCxTQUFTLEtBQUs7d0JBQ2hCM08sUUFBUUcsSUFBSSxDQUFDLG1DQUEyQyxPQUFsQndPLE9BQU8vTSxPQUFPLENBQUMsSUFBRzt3QkFDeERrRSxNQUFNOzRCQUNKMEUsT0FBTzs0QkFDUEMsYUFBYSx5QkFBMkMsT0FBbEJrRSxPQUFPL00sT0FBTyxDQUFDLElBQUc7NEJBQ3hEa0wsU0FBUzs0QkFDVHBDLFVBQVU7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsbUJBQW1CO1lBQ25CLE1BQU1tRTswREFBZTtvQkFDbkIsSUFBSTt3QkFDRix1REFBdUQ7d0JBQ3ZELE1BQU1DLG1CQUFtQmxCLGVBQWVFLG1CQUFtQjt3QkFDM0QsSUFBSWdCLGtCQUFrQjs0QkFDcEJsQixlQUFlbUIsV0FBVyxDQUN4QkQsa0JBQ0ExTCxNQUFNbEUsTUFBTSxFQUNaa0UsTUFBTWIsZUFBZSxFQUNyQmEsTUFBTVosWUFBWSxFQUNsQlksTUFBTVYsa0JBQWtCLEVBQ3hCVSxNQUFNUixjQUFjLEVBQ3BCUSxNQUFNUCxjQUFjLEVBQ3BCTyxNQUFNTixpQkFBaUIsRUFDdkJNLE1BQU1ULGVBQWUsS0FBSzt3QkFFOUI7d0JBRUEsc0NBQXNDO3dCQUN0Q1Esd0JBQXdCQztvQkFDMUIsRUFBRSxPQUFPM0MsT0FBTzt3QkFDZFQsUUFBUVMsS0FBSyxDQUFDLHFCQUFxQkE7b0JBQ3JDO2dCQUNGOztZQUVBME4sZ0JBQWdCYSxNQUFNLENBQUNILGNBQWMsUUFBUSw2QkFBNkI7WUFFMUUscUVBQXFFO1lBQ3JFLE1BQU1JO2dFQUFxQixDQUFDQztvQkFDMUIsaUNBQWlDO29CQUNqQ0w7b0JBRUEsa0NBQWtDO29CQUNsQyxJQUFJekwsTUFBTVQsZUFBZSxLQUFLLFdBQVc7d0JBQ3ZDLE1BQU00RSxVQUFVO3dCQUNoQjJILE1BQU1DLFdBQVcsR0FBRzVIO3dCQUNwQixPQUFPQTtvQkFDVDtnQkFDRjs7WUFFQTZILE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQko7WUFFeEMsbUJBQW1CO1lBQ25COzZDQUFPO29CQUNMWjtvQkFDQUk7b0JBQ0FOLGdCQUFnQm1CLE9BQU87b0JBQ3ZCRixPQUFPRyxtQkFBbUIsQ0FBQyxnQkFBZ0JOO2dCQUM3Qzs7UUFDRjtvQ0FBRztRQUFDN0w7UUFBTzBDO0tBQU07SUFFakIscUNBQXFDO0lBQ3JDM0gsZ0RBQVNBO3FDQUFDO1lBQ1IsTUFBTWdRLGtCQUFrQnBQLGlFQUFlQSxDQUFDOE8sV0FBVztZQUNuRE0sZ0JBQWdCcUIsT0FBTztRQUN6QjtvQ0FBRztRQUFDcE0sTUFBTVQsZUFBZTtLQUFDO0lBRzFCLGdCQUFnQjtJQUNoQixNQUFNOE0sZUFBbUM7UUFDdkMsR0FBR3JNLEtBQUs7UUFDUnlDO1FBQ0FvQztRQUNBOEQ7UUFDQXFCO1FBQ0FwSDtRQUNBK0c7UUFDQUc7UUFDQVQ7UUFDQTFKLGVBQWVLLE1BQU1MLGFBQWE7UUFDbENKLGlCQUFpQlMsTUFBTVQsZUFBZTtRQUN0QytNLGFBQWF0TSxNQUFNVCxlQUFlLEtBQUs7SUFDekM7SUFFQSxxQkFDRSw4REFBQzhDLGVBQWVrSyxRQUFRO1FBQUNDLE9BQU9IO2tCQUM3QjlKOzs7Ozs7QUFHUCxFQUFFO0dBeHlCV0Q7O1FBV08vRyxzREFBUUE7OztLQVhmK0c7QUEweUJiLHlDQUF5QztBQUNsQyxNQUFNbUssb0JBQW9COztJQUMvQixNQUFNQyxVQUFVN1IsaURBQVVBLENBQUN3SDtJQUMzQixJQUFJcUssWUFBWXhLLFdBQVc7UUFDekIsTUFBTSxJQUFJeUssTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtJQU5XRCIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXFRyYWRpbmdDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB0eXBlIHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVJlZHVjZXIsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgVHJhZGluZ0NvbmZpZywgVGFyZ2V0UHJpY2VSb3csIE9yZGVySGlzdG9yeUVudHJ5LCBBcHBTZXR0aW5ncywgT3JkZXJTdGF0dXMsIERpc3BsYXlPcmRlclJvdyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IERFRkFVTFRfQVBQX1NFVFRJTkdTLCBBVkFJTEFCTEVfQ1JZUFRPUywgQVZBSUxBQkxFX1FVT1RFU19TSU1QTEUsIEFWQUlMQUJMRV9TVEFCTEVDT0lOUyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnO1xuXG4vLyBEZWZpbmUgbG9jYWxseSB0byBhdm9pZCBpbXBvcnQgaXNzdWVzXG5jb25zdCBERUZBVUxUX1FVT1RFX0NVUlJFTkNJRVMgPSBbXCJVU0RUXCIsIFwiVVNEQ1wiLCBcIkJUQ1wiXTtcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCI7XG5pbXBvcnQgeyB0cmFkaW5nQXBpIH0gZnJvbSAnQC9saWIvYXBpJztcbmltcG9ydCB7IFNlc3Npb25NYW5hZ2VyIH0gZnJvbSAnQC9saWIvc2Vzc2lvbi1tYW5hZ2VyJztcbmltcG9ydCB7IE5ldHdvcmtNb25pdG9yLCBBdXRvU2F2ZU1hbmFnZXIsIE1lbW9yeU1vbml0b3IgfSBmcm9tICdAL2xpYi9uZXR3b3JrLW1vbml0b3InO1xuXG4vLyBEZWZpbmUgQm90IFN5c3RlbSBTdGF0dXNcbmV4cG9ydCB0eXBlIEJvdFN5c3RlbVN0YXR1cyA9ICdTdG9wcGVkJyB8ICdXYXJtaW5nVXAnIHwgJ1J1bm5pbmcnO1xuXG4vLyBVcGRhdGUgVGFyZ2V0UHJpY2VSb3cgdHlwZVxuLy8gKEFzc3VtaW5nIFRhcmdldFByaWNlUm93IGlzIGRlZmluZWQgaW4gQC9saWIvdHlwZXMsIHRoaXMgaXMgY29uY2VwdHVhbClcbi8vIGV4cG9ydCBpbnRlcmZhY2UgVGFyZ2V0UHJpY2VSb3cge1xuLy8gICAvLyAuLi4gZXhpc3RpbmcgZmllbGRzXG4vLyAgIGxhc3RBY3Rpb25UaW1lc3RhbXA/OiBudW1iZXI7XG4vLyB9XG5cbi8vIERlZmluZSBhY3Rpb24gdHlwZXNcbnR5cGUgQWN0aW9uID1cbiAgfCB7IHR5cGU6ICdTRVRfQ09ORklHJzsgcGF5bG9hZDogUGFydGlhbDxUcmFkaW5nQ29uZmlnPiB9XG4gIHwgeyB0eXBlOiAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3dbXSB9XG4gIHwgeyB0eXBlOiAnQUREX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnUkVNT1ZFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJzsgcGF5bG9hZDogT3JkZXJIaXN0b3J5RW50cnkgfVxuICB8IHsgdHlwZTogJ1VQREFURV9PUkRFUl9ISVNUT1JZX0VOVFJZJzsgcGF5bG9hZDogT3JkZXJIaXN0b3J5RW50cnkgfVxuICB8IHsgdHlwZTogJ1JFTU9WRV9PUkRFUl9ISVNUT1JZX0VOVFJZJzsgcGF5bG9hZDogc3RyaW5nIH1cbiAgfCB7IHR5cGU6ICdTRVRfQVBQX1NFVFRJTkdTJzsgcGF5bG9hZDogUGFydGlhbDxBcHBTZXR0aW5ncz4gfVxuICB8IHsgdHlwZTogJ1NFVF9CQUNLRU5EX1NUQVRVUyc7IHBheWxvYWQ6ICdvbmxpbmUnIHwgJ29mZmxpbmUnIH1cbiAgfCB7IHR5cGU6ICdTRVRfTUFSS0VUX1BSSUNFJzsgcGF5bG9hZDogbnVtYmVyIH1cbiAgfCB7IHR5cGU6ICdTRVRfQkFMQU5DRVMnOyBwYXlsb2FkOiB7IGNyeXB0bzE6IG51bWJlcjsgY3J5cHRvMjogbnVtYmVyIH0gfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9TVEFSVF9CT1RfSU5JVElBVEUnIH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fQ09NUExFVEVfV0FSTVVQJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX1NUT1BfQk9UJyB9XG4gIHwgeyB0eXBlOiAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3dbXSB9O1xuXG4vLyBEZWZpbmUgc3RhdGUgdHlwZVxuaW50ZXJmYWNlIFRyYWRpbmdTdGF0ZSB7XG4gIGNvbmZpZzogVHJhZGluZ0NvbmZpZztcbiAgdGFyZ2V0UHJpY2VSb3dzOiBUYXJnZXRQcmljZVJvd1tdO1xuICBvcmRlckhpc3Rvcnk6IE9yZGVySGlzdG9yeUVudHJ5W107XG4gIGFwcFNldHRpbmdzOiBBcHBTZXR0aW5ncztcbiAgYmFja2VuZFN0YXR1czogJ29ubGluZScgfCAnb2ZmbGluZScgfCAndW5rbm93bic7XG4gIGN1cnJlbnRNYXJrZXRQcmljZTogbnVtYmVyO1xuICBjcnlwdG8xQmFsYW5jZTogbnVtYmVyO1xuICBjcnlwdG8yQmFsYW5jZTogbnVtYmVyO1xuICAvLyBpc0JvdEFjdGl2ZTogYm9vbGVhbjsgLy8gVG8gYmUgcmVwbGFjZWQgYnkgYm90U3lzdGVtU3RhdHVzXG4gIGJvdFN5c3RlbVN0YXR1czogQm90U3lzdGVtU3RhdHVzOyAvLyBOZXcgc3RhdGUgZm9yIGJvdCBsaWZlY3ljbGVcbiAgc3RhYmxlY29pbkJhbGFuY2U6IG51bWJlcjsgLy8gRm9yIFN0YWJsZWNvaW4gU3dhcCBtb2RlXG59XG5cbi8vIEFkZCB0aGlzIGZ1bmN0aW9uIHRvIGNhbGN1bGF0ZSB0aGUgaW5pdGlhbCBtYXJrZXQgcHJpY2VcbmNvbnN0IGNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZSA9IChjb25maWc6IFRyYWRpbmdDb25maWcpOiBudW1iZXIgPT4ge1xuICAvLyBSZXR1cm4gMCBpZiBlaXRoZXIgY3J5cHRvIGlzIG5vdCBzZWxlY3RlZFxuICBpZiAoIWNvbmZpZy5jcnlwdG8xIHx8ICFjb25maWcuY3J5cHRvMikge1xuICAgIHJldHVybiAwO1xuICB9XG4gIC8vIERlZmF1bHQgZmFsbGJhY2sgdmFsdWUgZm9yIHZhbGlkIHBhaXJzXG4gIHJldHVybiBjYWxjdWxhdGVGYWxsYmFja01hcmtldFByaWNlKGNvbmZpZyk7XG59O1xuXG4vLyBFbmhhbmNlZCBBUEkgZnVuY3Rpb24gdG8gZ2V0IG1hcmtldCBwcmljZSBmb3IgYW55IHRyYWRpbmcgcGFpclxuY29uc3QgZ2V0TWFya2V0UHJpY2VGcm9tQVBJID0gYXN5bmMgKGNvbmZpZzogVHJhZGluZ0NvbmZpZyk6IFByb21pc2U8bnVtYmVyPiA9PiB7XG4gIHRyeSB7XG4gICAgLy8gUmV0dXJuIDAgaWYgZWl0aGVyIGNyeXB0byBpcyBub3Qgc2VsZWN0ZWRcbiAgICBpZiAoIWNvbmZpZy5jcnlwdG8xIHx8ICFjb25maWcuY3J5cHRvMikge1xuICAgICAgcmV0dXJuIDA7XG4gICAgfVxuICAgIC8vIFRyeSBtdWx0aXBsZSBBUEkgZW5kcG9pbnRzIGZvciBiZXR0ZXIgY292ZXJhZ2VcbiAgICBjb25zdCBzeW1ib2wgPSBgJHtjb25maWcuY3J5cHRvMX0ke2NvbmZpZy5jcnlwdG8yfWAudG9VcHBlckNhc2UoKTtcblxuICAgIC8vIEZpcnN0IHRyeSBCaW5hbmNlIEFQSVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwczovL2FwaS5iaW5hbmNlLmNvbS9hcGkvdjMvdGlja2VyL3ByaWNlP3N5bWJvbD0ke3N5bWJvbH1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zdCBwcmljZSA9IHBhcnNlRmxvYXQoZGF0YS5wcmljZSk7XG4gICAgICAgIGlmIChwcmljZSA+IDApIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFByaWNlIGZldGNoZWQgZnJvbSBCaW5hbmNlOiAke2NvbmZpZy5jcnlwdG8xfS8ke2NvbmZpZy5jcnlwdG8yfSA9ICR7cHJpY2V9YCk7XG4gICAgICAgICAgcmV0dXJuIHByaWNlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoYmluYW5jZUVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0JpbmFuY2UgQVBJIGZhaWxlZCwgdHJ5aW5nIGFsdGVybmF0aXZlLi4uJywgYmluYW5jZUVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBDb2luR2Vja28gQVBJIGZvciBicm9hZGVyIHBhaXIgc3VwcG9ydFxuICAgIHRyeSB7XG4gICAgICBjb25zdCBjcnlwdG8xSWQgPSBnZXRDb2luR2Vja29JZChjb25maWcuY3J5cHRvMSk7XG4gICAgICBjb25zdCBjcnlwdG8ySWQgPSBnZXRDb2luR2Vja29JZChjb25maWcuY3J5cHRvMik7XG5cbiAgICAgIGlmIChjcnlwdG8xSWQgJiYgY3J5cHRvMklkKSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vYXBpLmNvaW5nZWNrby5jb20vYXBpL3YzL3NpbXBsZS9wcmljZT9pZHM9JHtjcnlwdG8xSWR9JnZzX2N1cnJlbmNpZXM9JHtjcnlwdG8ySWR9YCk7XG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgY29uc3QgcHJpY2UgPSBkYXRhW2NyeXB0bzFJZF0/LltjcnlwdG8ySWRdO1xuICAgICAgICAgIGlmIChwcmljZSA+IDApIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgUHJpY2UgZmV0Y2hlZCBmcm9tIENvaW5HZWNrbzogJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn0gPSAke3ByaWNlfWApO1xuICAgICAgICAgICAgcmV0dXJuIHByaWNlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGdlY2tvRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignQ29pbkdlY2tvIEFQSSBmYWlsZWQsIHVzaW5nIG1vY2sgcHJpY2UuLi4nLCBnZWNrb0Vycm9yKTtcbiAgICB9XG5cbiAgICAvLyBGaW5hbCBmYWxsYmFjayB0byBtb2NrIHByaWNlXG4gICAgY29uc3QgbW9ja1ByaWNlID0gY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZShjb25maWcpO1xuICAgIGNvbnNvbGUubG9nKGDimqDvuI8gVXNpbmcgbW9jayBwcmljZTogJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcuY3J5cHRvMn0gPSAke21vY2tQcmljZX1gKTtcbiAgICByZXR1cm4gbW9ja1ByaWNlO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgbWFya2V0IHByaWNlOicsIGVycm9yKTtcbiAgICByZXR1cm4gY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZShjb25maWcpO1xuICB9XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gbWFwIGNyeXB0byBzeW1ib2xzIHRvIENvaW5HZWNrbyBJRHNcbmNvbnN0IGdldENvaW5HZWNrb0lkID0gKHN5bWJvbDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCA9PiB7XG4gIGNvbnN0IG1hcHBpbmc6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgJ0JUQyc6ICdiaXRjb2luJyxcbiAgICAnRVRIJzogJ2V0aGVyZXVtJyxcbiAgICAnU09MJzogJ3NvbGFuYScsXG4gICAgJ0FEQSc6ICdjYXJkYW5vJyxcbiAgICAnRE9UJzogJ3BvbGthZG90JyxcbiAgICAnTUFUSUMnOiAnbWF0aWMtbmV0d29yaycsXG4gICAgJ0FWQVgnOiAnYXZhbGFuY2hlLTInLFxuICAgICdMSU5LJzogJ2NoYWlubGluaycsXG4gICAgJ1VOSSc6ICd1bmlzd2FwJyxcbiAgICAnVVNEVCc6ICd0ZXRoZXInLFxuICAgICdVU0RDJzogJ3VzZC1jb2luJyxcbiAgICAnQlVTRCc6ICdiaW5hbmNlLXVzZCcsXG4gICAgJ0RBSSc6ICdkYWknXG4gIH07XG4gIHJldHVybiBtYXBwaW5nW3N5bWJvbC50b1VwcGVyQ2FzZSgpXSB8fCBudWxsO1xufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBzdGFibGVjb2luIGV4Y2hhbmdlIHJhdGVzIGZvciByZWFsIG1hcmtldCBkYXRhXG5jb25zdCBnZXRTdGFibGVjb2luRXhjaGFuZ2VSYXRlID0gYXN5bmMgKGNyeXB0bzogc3RyaW5nLCBzdGFibGVjb2luOiBzdHJpbmcpOiBQcm9taXNlPG51bWJlcj4gPT4ge1xuICB0cnkge1xuICAgIC8vIEZvciBzdGFibGVjb2luLXRvLXN0YWJsZWNvaW4sIGFzc3VtZSAxOjEgcmF0ZVxuICAgIGlmIChnZXRDb2luR2Vja29JZChjcnlwdG8pICYmIGdldENvaW5HZWNrb0lkKHN0YWJsZWNvaW4pKSB7XG4gICAgICBjb25zdCBjcnlwdG9JZCA9IGdldENvaW5HZWNrb0lkKGNyeXB0byk7XG4gICAgICBjb25zdCBzdGFibGVjb2luSWQgPSBnZXRDb2luR2Vja29JZChzdGFibGVjb2luKTtcblxuICAgICAgaWYgKGNyeXB0b0lkID09PSBzdGFibGVjb2luSWQpIHJldHVybiAxLjA7IC8vIFNhbWUgY3VycmVuY3lcblxuICAgICAgLy8gR2V0IHJlYWwgZXhjaGFuZ2UgcmF0ZSBmcm9tIENvaW5HZWNrb1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9hcGkuY29pbmdlY2tvLmNvbS9hcGkvdjMvc2ltcGxlL3ByaWNlP2lkcz0ke2NyeXB0b0lkfSZ2c19jdXJyZW5jaWVzPSR7c3RhYmxlY29pbklkfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IHJhdGUgPSBjcnlwdG9JZCAmJiBzdGFibGVjb2luSWQgPyBkYXRhW2NyeXB0b0lkXT8uW3N0YWJsZWNvaW5JZF0gOiBudWxsO1xuICAgICAgICBpZiAocmF0ZSA+IDApIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+TiiBTdGFibGVjb2luIHJhdGU6ICR7Y3J5cHRvfS8ke3N0YWJsZWNvaW59ID0gJHtyYXRlfWApO1xuICAgICAgICAgIHJldHVybiByYXRlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRmFsbGJhY2s6IGNhbGN1bGF0ZSB2aWEgVVNEIHByaWNlc1xuICAgIGNvbnN0IGNyeXB0b1VTRFByaWNlID0gZ2V0VVNEUHJpY2UoY3J5cHRvKTtcbiAgICBjb25zdCBzdGFibGVjb2luVVNEUHJpY2UgPSBnZXRVU0RQcmljZShzdGFibGVjb2luKTtcbiAgICBjb25zdCByYXRlID0gY3J5cHRvVVNEUHJpY2UgLyBzdGFibGVjb2luVVNEUHJpY2U7XG5cbiAgICBjb25zb2xlLmxvZyhg8J+TiiBGYWxsYmFjayBzdGFibGVjb2luIHJhdGU6ICR7Y3J5cHRvfS8ke3N0YWJsZWNvaW59ID0gJHtyYXRlfSAodmlhIFVTRClgKTtcbiAgICByZXR1cm4gcmF0ZTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHN0YWJsZWNvaW4gZXhjaGFuZ2UgcmF0ZTonLCBlcnJvcik7XG4gICAgLy8gRmluYWwgZmFsbGJhY2tcbiAgICBjb25zdCBjcnlwdG9VU0RQcmljZSA9IGdldFVTRFByaWNlKGNyeXB0byk7XG4gICAgY29uc3Qgc3RhYmxlY29pblVTRFByaWNlID0gZ2V0VVNEUHJpY2Uoc3RhYmxlY29pbik7XG4gICAgcmV0dXJuIGNyeXB0b1VTRFByaWNlIC8gc3RhYmxlY29pblVTRFByaWNlO1xuICB9XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IFVTRCBwcmljZSAoZXh0cmFjdGVkIGZyb20gY2FsY3VsYXRlRmFsbGJhY2tNYXJrZXRQcmljZSlcbmNvbnN0IGdldFVTRFByaWNlID0gKGNyeXB0bzogc3RyaW5nKTogbnVtYmVyID0+IHtcbiAgY29uc3QgdXNkUHJpY2VzOiB7IFtrZXk6IHN0cmluZ106IG51bWJlciB9ID0ge1xuICAgIC8vIE1ham9yIGNyeXB0b2N1cnJlbmNpZXNcbiAgICAnQlRDJzogMTA5MDAwLFxuICAgICdFVEgnOiA0MDAwLFxuICAgICdTT0wnOiAyNDAsXG4gICAgJ0FEQSc6IDEuMixcbiAgICAnRE9HRSc6IDAuNCxcbiAgICAnTElOSyc6IDI1LFxuICAgICdNQVRJQyc6IDAuNSxcbiAgICAnRE9UJzogOCxcbiAgICAnQVZBWCc6IDQ1LFxuICAgICdTSElCJzogMC4wMDAwMzAsXG4gICAgJ1hSUCc6IDIuNSxcbiAgICAnTFRDJzogMTEwLFxuICAgICdCQ0gnOiA1MDAsXG5cbiAgICAvLyBEZUZpIHRva2Vuc1xuICAgICdVTkknOiAxNSxcbiAgICAnQUFWRSc6IDE4MCxcbiAgICAnTUtSJzogMTgwMCxcbiAgICAnU05YJzogMy41LFxuICAgICdDT01QJzogODUsXG4gICAgJ1lGSSc6IDg1MDAsXG4gICAgJ1NVU0hJJzogMi4xLFxuICAgICcxSU5DSCc6IDAuNjUsXG4gICAgJ0NSVic6IDAuODUsXG4gICAgJ1VNQSc6IDMuMixcblxuICAgIC8vIExheWVyIDEgYmxvY2tjaGFpbnNcbiAgICAnQVRPTSc6IDEyLFxuICAgICdORUFSJzogNi41LFxuICAgICdBTEdPJzogMC4zNSxcbiAgICAnSUNQJzogMTQsXG4gICAgJ0hCQVInOiAwLjI4LFxuICAgICdBUFQnOiAxMi41LFxuICAgICdUT04nOiA1LjgsXG4gICAgJ0ZUTSc6IDAuOTUsXG4gICAgJ09ORSc6IDAuMDI1LFxuXG4gICAgLy8gT3RoZXIgcG9wdWxhciB0b2tlbnNcbiAgICAnRklMJzogOC41LFxuICAgICdUUlgnOiAwLjI1LFxuICAgICdFVEMnOiAzNSxcbiAgICAnVkVUJzogMC4wNTUsXG4gICAgJ1FOVCc6IDEyNSxcbiAgICAnTERPJzogMi44LFxuICAgICdDUk8nOiAwLjE4LFxuICAgICdMVU5DJzogMC4wMDAxNSxcblxuICAgIC8vIEdhbWluZyAmIE1ldGF2ZXJzZVxuICAgICdNQU5BJzogMC44NSxcbiAgICAnU0FORCc6IDAuNzUsXG4gICAgJ0FYUyc6IDguNSxcbiAgICAnRU5KJzogMC40NSxcbiAgICAnQ0haJzogMC4xMixcblxuICAgIC8vIEluZnJhc3RydWN0dXJlICYgVXRpbGl0eVxuICAgICdUSEVUQSc6IDIuMSxcbiAgICAnRkxPVyc6IDEuMixcbiAgICAnWFRaJzogMS44LFxuICAgICdFT1MnOiAxLjEsXG4gICAgJ0dSVCc6IDAuMjgsXG4gICAgJ0JBVCc6IDAuMzUsXG5cbiAgICAvLyBQcml2YWN5IGNvaW5zXG4gICAgJ1pFQyc6IDQ1LFxuICAgICdEQVNIJzogMzUsXG5cbiAgICAvLyBERVggdG9rZW5zXG4gICAgJ0xSQyc6IDAuNDUsXG4gICAgJ1pSWCc6IDAuNjUsXG4gICAgJ0tOQyc6IDAuODUsXG5cbiAgICAvLyBPdGhlciB0b2tlbnNcbiAgICAnUkVOJzogMC4xNSxcbiAgICAnQkFORCc6IDIuNSxcbiAgICAnU1RPUkonOiAwLjg1LFxuICAgICdOTVInOiAyNSxcbiAgICAnQU5UJzogOC41LFxuICAgICdCTlQnOiAwLjk1LFxuICAgICdNTE4nOiAzNSxcbiAgICAnUkVQJzogMTUsXG5cbiAgICAvLyBTbWFsbGVyIGNhcCB0b2tlbnNcbiAgICAnSU9UWCc6IDAuMDY1LFxuICAgICdaSUwnOiAwLjA0NSxcbiAgICAnSUNYJzogMC4zNSxcbiAgICAnUVRVTSc6IDQuNSxcbiAgICAnT05UJzogMC40NSxcbiAgICAnV0FWRVMnOiAzLjIsXG4gICAgJ0xTSyc6IDEuOCxcbiAgICAnTkFOTyc6IDEuNSxcbiAgICAnU0MnOiAwLjAwOCxcbiAgICAnREdCJzogMC4wMjUsXG4gICAgJ1JWTic6IDAuMDM1LFxuICAgICdCVFQnOiAwLjAwMDAwMTUsXG4gICAgJ1dJTic6IDAuMDAwMTUsXG4gICAgJ0hPVCc6IDAuMDAzNSxcbiAgICAnREVOVCc6IDAuMDAxOCxcbiAgICAnTlBYUyc6IDAuMDAwODUsXG4gICAgJ0ZVTic6IDAuMDA4NSxcbiAgICAnQ0VMUic6IDAuMDI1LFxuXG4gICAgLy8gU3RhYmxlY29pbnNcbiAgICAnVVNEVCc6IDEuMCxcbiAgICAnVVNEQyc6IDEuMCxcbiAgICAnRkRVU0QnOiAxLjAsXG4gICAgJ0JVU0QnOiAxLjAsXG4gICAgJ0RBSSc6IDEuMFxuICB9O1xuICByZXR1cm4gdXNkUHJpY2VzW2NyeXB0by50b1VwcGVyQ2FzZSgpXSB8fCAxMDA7XG59O1xuXG4vLyBFbmhhbmNlZCBmYWxsYmFjayBmdW5jdGlvbiBmb3IgbWFya2V0IHByaWNlIGNhbGN1bGF0aW9uIHN1cHBvcnRpbmcgYWxsIHRyYWRpbmcgcGFpcnNcbmNvbnN0IGNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UgPSAoY29uZmlnOiBUcmFkaW5nQ29uZmlnKTogbnVtYmVyID0+IHtcbiAgY29uc3QgY3J5cHRvMVVTRFByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzEpO1xuICBjb25zdCBjcnlwdG8yVVNEUHJpY2UgPSBnZXRVU0RQcmljZShjb25maWcuY3J5cHRvMik7XG5cbiAgLy8gQ2FsY3VsYXRlIHRoZSByYXRpbzogaG93IG1hbnkgdW5pdHMgb2YgY3J5cHRvMiA9IDEgdW5pdCBvZiBjcnlwdG8xXG4gIGNvbnN0IGJhc2VQcmljZSA9IGNyeXB0bzFVU0RQcmljZSAvIGNyeXB0bzJVU0RQcmljZTtcblxuICAvLyBBZGQgc21hbGwgcmFuZG9tIGZsdWN0dWF0aW9uXG4gIGNvbnN0IGZsdWN0dWF0aW9uID0gKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4wMjsgLy8gwrExJVxuICBjb25zdCBmaW5hbFByaWNlID0gYmFzZVByaWNlICogKDEgKyBmbHVjdHVhdGlvbik7XG5cbiAgY29uc29sZS5sb2coYPCfk4ogRmFsbGJhY2sgcHJpY2UgY2FsY3VsYXRpb246ICR7Y29uZmlnLmNyeXB0bzF9ICgkJHtjcnlwdG8xVVNEUHJpY2V9KSAvICR7Y29uZmlnLmNyeXB0bzJ9ICgkJHtjcnlwdG8yVVNEUHJpY2V9KSA9ICR7ZmluYWxQcmljZS50b0ZpeGVkKDYpfWApO1xuXG4gIHJldHVybiBmaW5hbFByaWNlO1xufTtcblxudHlwZSBUcmFkaW5nQWN0aW9uID1cbiAgfCB7IHR5cGU6ICdTRVRfQ09ORklHJzsgcGF5bG9hZDogUGFydGlhbDxUcmFkaW5nQ29uZmlnPiB9XG4gIHwgeyB0eXBlOiAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3dbXSB9XG4gIHwgeyB0eXBlOiAnQUREX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBUYXJnZXRQcmljZVJvdyB9XG4gIHwgeyB0eXBlOiAnUkVNT1ZFX1RBUkdFVF9QUklDRV9ST1cnOyBwYXlsb2FkOiBzdHJpbmcgfVxuICB8IHsgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJzsgcGF5bG9hZDogT3JkZXJIaXN0b3J5RW50cnkgfVxuICB8IHsgdHlwZTogJ1NFVF9BUFBfU0VUVElOR1MnOyBwYXlsb2FkOiBQYXJ0aWFsPEFwcFNldHRpbmdzPiB9XG4gIHwgeyB0eXBlOiAnU0VUX01BUktFVF9QUklDRSc7IHBheWxvYWQ6IG51bWJlciB9XG4gIHwgeyB0eXBlOiAnRkxVQ1RVQVRFX01BUktFVF9QUklDRScgfVxuICAvLyB8IHsgdHlwZTogJ1NFVF9CT1RfU1RBVFVTJzsgcGF5bG9hZDogYm9vbGVhbiB9IC8vIFJlbW92ZWRcbiAgfCB7IHR5cGU6ICdVUERBVEVfQkFMQU5DRVMnOyBwYXlsb2FkOiB7IGNyeXB0bzE/OiBudW1iZXI7IGNyeXB0bzI/OiBudW1iZXI7IHN0YWJsZWNvaW4/OiBudW1iZXIgfSB9XG4gIHwgeyB0eXBlOiAnVVBEQVRFX1NUQUJMRUNPSU5fQkFMQU5DRSc7IHBheWxvYWQ6IG51bWJlciB9XG4gIHwgeyB0eXBlOiAnUkVTRVRfU0VTU0lPTicgfVxuICB8IHsgdHlwZTogJ0NMRUFSX09SREVSX0hJU1RPUlknIH1cbiAgfCB7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnOyBwYXlsb2FkOiAnb25saW5lJyB8ICdvZmZsaW5lJyB8ICd1bmtub3duJyB9XG4gIHwgeyB0eXBlOiAnU1lTVEVNX1NUQVJUX0JPVF9JTklUSUFURScgfVxuICB8IHsgdHlwZTogJ1NZU1RFTV9DT01QTEVURV9XQVJNVVAnIH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fU1RPUF9CT1QnIH1cbiAgfCB7IHR5cGU6ICdTWVNURU1fUkVTRVRfQk9UJyB9XG4gIHwgeyB0eXBlOiAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzsgcGF5bG9hZDogVGFyZ2V0UHJpY2VSb3dbXSB9O1xuXG5jb25zdCBpbml0aWFsQmFzZUNvbmZpZzogVHJhZGluZ0NvbmZpZyA9IHtcbiAgdHJhZGluZ01vZGU6IFwiU2ltcGxlU3BvdFwiLFxuICBjcnlwdG8xOiBcIlwiLCAvLyBTdGFydCB3aXRoIGVtcHR5IGNyeXB0bzFcbiAgY3J5cHRvMjogXCJcIiwgLy8gU3RhcnQgd2l0aCBlbXB0eSBjcnlwdG8yXG4gIGJhc2VCaWQ6IDEwMCxcbiAgbXVsdGlwbGllcjogMS4wMDUsXG4gIG51bURpZ2l0czogNCxcbiAgc2xpcHBhZ2VQZXJjZW50OiAwLjIsIC8vIERlZmF1bHQgMC4yJSBzbGlwcGFnZSByYW5nZVxuICBpbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50OiA1MCxcbiAgaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudDogNTAsXG4gIHByZWZlcnJlZFN0YWJsZWNvaW46IEFWQUlMQUJMRV9TVEFCTEVDT0lOU1swXSxcbn07XG5cbmNvbnN0IGluaXRpYWxUcmFkaW5nU3RhdGU6IFRyYWRpbmdTdGF0ZSA9IHtcbiAgY29uZmlnOiBpbml0aWFsQmFzZUNvbmZpZyxcbiAgdGFyZ2V0UHJpY2VSb3dzOiBbXSxcbiAgb3JkZXJIaXN0b3J5OiBbXSxcbiAgYXBwU2V0dGluZ3M6IERFRkFVTFRfQVBQX1NFVFRJTkdTLFxuICBjdXJyZW50TWFya2V0UHJpY2U6IGNhbGN1bGF0ZUluaXRpYWxNYXJrZXRQcmljZShpbml0aWFsQmFzZUNvbmZpZyksXG4gIC8vIGlzQm90QWN0aXZlOiBmYWxzZSwgLy8gUmVtb3ZlXG4gIGJvdFN5c3RlbVN0YXR1czogJ1N0b3BwZWQnLCAvLyBJbml0aWFsaXplIGFzIFN0b3BwZWRcbiAgY3J5cHRvMUJhbGFuY2U6IDEwLCAvLyBNb2NrIGluaXRpYWwgYmFsYW5jZVxuICBjcnlwdG8yQmFsYW5jZTogMTAwMDAwLCAvLyBJbmNyZWFzZWQgYmFsYW5jZSBmb3IgQlRDL1VTRFQgdHJhZGluZyAod2FzIDEwMDAwKVxuICBzdGFibGVjb2luQmFsYW5jZTogMCxcbiAgYmFja2VuZFN0YXR1czogJ3Vua25vd24nLFxufTtcblxuY29uc3QgbGFzdEFjdGlvblRpbWVzdGFtcFBlckNvdW50ZXIgPSBuZXcgTWFwPG51bWJlciwgbnVtYmVyPigpO1xuXG4vLyBMb2NhbFN0b3JhZ2UgcGVyc2lzdGVuY2UgZnVuY3Rpb25zXG5jb25zdCBTVE9SQUdFX0tFWSA9ICdwbHV0b190cmFkaW5nX3N0YXRlJztcblxuY29uc3Qgc2F2ZVN0YXRlVG9Mb2NhbFN0b3JhZ2UgPSAoc3RhdGU6IFRyYWRpbmdTdGF0ZSkgPT4ge1xuICB0cnkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3Qgc3RhdGVUb1NhdmUgPSB7XG4gICAgICAgIGNvbmZpZzogc3RhdGUuY29uZmlnLFxuICAgICAgICB0YXJnZXRQcmljZVJvd3M6IHN0YXRlLnRhcmdldFByaWNlUm93cyxcbiAgICAgICAgb3JkZXJIaXN0b3J5OiBzdGF0ZS5vcmRlckhpc3RvcnksXG4gICAgICAgIGFwcFNldHRpbmdzOiBzdGF0ZS5hcHBTZXR0aW5ncyxcbiAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgIGNyeXB0bzFCYWxhbmNlOiBzdGF0ZS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgY3J5cHRvMkJhbGFuY2U6IHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogc3RhdGUuc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIGJvdFN5c3RlbVN0YXR1czogc3RhdGUuYm90U3lzdGVtU3RhdHVzLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH07XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTVE9SQUdFX0tFWSwgSlNPTi5zdHJpbmdpZnkoc3RhdGVUb1NhdmUpKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgc3RhdGUgdG8gbG9jYWxTdG9yYWdlOicsIGVycm9yKTtcbiAgfVxufTtcblxuY29uc3QgbG9hZFN0YXRlRnJvbUxvY2FsU3RvcmFnZSA9ICgpOiBQYXJ0aWFsPFRyYWRpbmdTdGF0ZT4gfCBudWxsID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnN0IHNhdmVkU3RhdGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTVE9SQUdFX0tFWSk7XG4gICAgICBpZiAoc2F2ZWRTdGF0ZSkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHNhdmVkU3RhdGUpO1xuICAgICAgICAvLyBDaGVjayBpZiBzdGF0ZSBpcyBub3QgdG9vIG9sZCAoMjQgaG91cnMpXG4gICAgICAgIGlmIChwYXJzZWQudGltZXN0YW1wICYmIChEYXRlLm5vdygpIC0gcGFyc2VkLnRpbWVzdGFtcCkgPCAyNCAqIDYwICogNjAgKiAxMDAwKSB7XG4gICAgICAgICAgcmV0dXJuIHBhcnNlZDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZTonLCBlcnJvcik7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59O1xuXG5jb25zdCB0cmFkaW5nUmVkdWNlciA9IChzdGF0ZTogVHJhZGluZ1N0YXRlLCBhY3Rpb246IFRyYWRpbmdBY3Rpb24pOiBUcmFkaW5nU3RhdGUgPT4ge1xuICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgY2FzZSAnU0VUX0NPTkZJRyc6XG4gICAgICBjb25zdCBuZXdDb25maWcgPSB7IC4uLnN0YXRlLmNvbmZpZywgLi4uYWN0aW9uLnBheWxvYWQgfTtcbiAgICAgIC8vIElmIHRyYWRpbmcgcGFpciBjaGFuZ2VzLCByZXNldCBtYXJrZXQgcHJpY2UgKGl0IHdpbGwgYmUgcmUtY2FsY3VsYXRlZCBieSBlZmZlY3QpXG4gICAgICBpZiAoYWN0aW9uLnBheWxvYWQuY3J5cHRvMSB8fCBhY3Rpb24ucGF5bG9hZC5jcnlwdG8yKSB7XG4gICAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBjb25maWc6IG5ld0NvbmZpZywgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UobmV3Q29uZmlnKSB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGNvbmZpZzogbmV3Q29uZmlnIH07XG4gICAgY2FzZSAnU0VUX1RBUkdFVF9QUklDRV9ST1dTJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCB0YXJnZXRQcmljZVJvd3M6IGFjdGlvbi5wYXlsb2FkLnNvcnQoKGE6IFRhcmdldFByaWNlUm93LCBiOiBUYXJnZXRQcmljZVJvdykgPT4gYS50YXJnZXRQcmljZSAtIGIudGFyZ2V0UHJpY2UpLm1hcCgocm93OiBUYXJnZXRQcmljZVJvdywgaW5kZXg6IG51bWJlcikgPT4gKHsuLi5yb3csIGNvdW50ZXI6IGluZGV4ICsgMX0pKSB9O1xuICAgIGNhc2UgJ0FERF9UQVJHRVRfUFJJQ0VfUk9XJzoge1xuICAgICAgY29uc3QgbmV3Um93cyA9IFsuLi5zdGF0ZS50YXJnZXRQcmljZVJvd3MsIGFjdGlvbi5wYXlsb2FkXS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKS5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3csIGluZGV4OiBudW1iZXIpID0+ICh7Li4ucm93LCBjb3VudGVyOiBpbmRleCArIDF9KSk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiBuZXdSb3dzIH07XG4gICAgfVxuICAgIGNhc2UgJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJzoge1xuICAgICAgY29uc3QgdXBkYXRlZFJvd3MgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKChyb3c6IFRhcmdldFByaWNlUm93KSA9PlxuICAgICAgICByb3cuaWQgPT09IGFjdGlvbi5wYXlsb2FkLmlkID8gYWN0aW9uLnBheWxvYWQgOiByb3dcbiAgICAgICkuc29ydCgoYTogVGFyZ2V0UHJpY2VSb3csIGI6IFRhcmdldFByaWNlUm93KSA9PiBhLnRhcmdldFByaWNlIC0gYi50YXJnZXRQcmljZSkubWFwKChyb3c6IFRhcmdldFByaWNlUm93LCBpbmRleDogbnVtYmVyKSA9PiAoey4uLnJvdywgY291bnRlcjogaW5kZXggKyAxfSkpO1xuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIHRhcmdldFByaWNlUm93czogdXBkYXRlZFJvd3MgfTtcbiAgICB9XG4gICAgY2FzZSAnUkVNT1ZFX1RBUkdFVF9QUklDRV9ST1cnOiB7XG4gICAgICBjb25zdCBmaWx0ZXJlZFJvd3MgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MuZmlsdGVyKChyb3c6IFRhcmdldFByaWNlUm93KSA9PiByb3cuaWQgIT09IGFjdGlvbi5wYXlsb2FkKS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKS5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3csIGluZGV4OiBudW1iZXIpID0+ICh7Li4ucm93LCBjb3VudGVyOiBpbmRleCArIDF9KSk7XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgdGFyZ2V0UHJpY2VSb3dzOiBmaWx0ZXJlZFJvd3MgfTtcbiAgICB9XG4gICAgY2FzZSAnQUREX09SREVSX0hJU1RPUllfRU5UUlknOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIG9yZGVySGlzdG9yeTogW2FjdGlvbi5wYXlsb2FkLCAuLi5zdGF0ZS5vcmRlckhpc3RvcnldIH07XG4gICAgY2FzZSAnQ0xFQVJfT1JERVJfSElTVE9SWSc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgb3JkZXJIaXN0b3J5OiBbXSB9O1xuICAgIGNhc2UgJ1NFVF9BUFBfU0VUVElOR1MnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGFwcFNldHRpbmdzOiB7IC4uLnN0YXRlLmFwcFNldHRpbmdzLCAuLi5hY3Rpb24ucGF5bG9hZCB9IH07XG4gICAgY2FzZSAnU0VUX01BUktFVF9QUklDRSc6XG4gICAgICByZXR1cm4geyAuLi5zdGF0ZSwgY3VycmVudE1hcmtldFByaWNlOiBhY3Rpb24ucGF5bG9hZCB9O1xuICAgIGNhc2UgJ0ZMVUNUVUFURV9NQVJLRVRfUFJJQ0UnOiB7XG4gICAgICBpZiAoc3RhdGUuY3VycmVudE1hcmtldFByaWNlIDw9IDApIHJldHVybiBzdGF0ZTtcbiAgICAgIC8vIE1vcmUgcmVhbGlzdGljIGZsdWN0dWF0aW9uOiBzbWFsbGVyLCBtb3JlIGZyZXF1ZW50IGNoYW5nZXNcbiAgICAgIGNvbnN0IGZsdWN0dWF0aW9uRmFjdG9yID0gKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMC4wMDY7IC8vIEFwcHJveCArLy0gMC4zJSBwZXIgdXBkYXRlXG4gICAgICBjb25zdCBuZXdQcmljZSA9IHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSAqICgxICsgZmx1Y3R1YXRpb25GYWN0b3IpO1xuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGN1cnJlbnRNYXJrZXRQcmljZTogbmV3UHJpY2UgPiAwID8gbmV3UHJpY2UgOiBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UgfTtcbiAgICB9XG4gICAgLy8gY2FzZSAnU0VUX0JPVF9TVEFUVVMnOiAvLyBSZW1vdmVkXG4gICAgY2FzZSAnVVBEQVRFX0JBTEFOQ0VTJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBjcnlwdG8xQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMSAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuY3J5cHRvMSA6IHN0YXRlLmNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuY3J5cHRvMiAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuY3J5cHRvMiA6IHN0YXRlLmNyeXB0bzJCYWxhbmNlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogYWN0aW9uLnBheWxvYWQuc3RhYmxlY29pbiAhPT0gdW5kZWZpbmVkID8gYWN0aW9uLnBheWxvYWQuc3RhYmxlY29pbiA6IHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgfTtcbiAgICBjYXNlICdVUERBVEVfU1RBQkxFQ09JTl9CQUxBTkNFJzpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogYWN0aW9uLnBheWxvYWQsXG4gICAgICB9O1xuICAgIGNhc2UgJ1JFU0VUX1NFU1NJT04nOlxuICAgICAgY29uc3QgY29uZmlnRm9yUmVzZXQgPSB7IC4uLnN0YXRlLmNvbmZpZyB9O1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSxcbiAgICAgICAgY29uZmlnOiBjb25maWdGb3JSZXNldCxcbiAgICAgICAgYXBwU2V0dGluZ3M6IHsgLi4uc3RhdGUuYXBwU2V0dGluZ3MgfSxcbiAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiBjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UoY29uZmlnRm9yUmVzZXQpLFxuICAgICAgfTtcbiAgICBjYXNlICdTRVRfQkFDS0VORF9TVEFUVVMnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGJhY2tlbmRTdGF0dXM6IGFjdGlvbi5wYXlsb2FkIH07XG4gICAgY2FzZSAnU1lTVEVNX1NUQVJUX0JPVF9JTklUSUFURSc6XG4gICAgICAvLyBDb250aW51ZSBmcm9tIHByZXZpb3VzIHN0YXRlIGluc3RlYWQgb2YgcmVzZXR0aW5nXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgYm90U3lzdGVtU3RhdHVzOiAnV2FybWluZ1VwJyxcbiAgICAgICAgLy8gS2VlcCBleGlzdGluZyB0YXJnZXRQcmljZVJvd3MgYW5kIG9yZGVySGlzdG9yeSAtIGRvbid0IHJlc2V0XG4gICAgICB9O1xuICAgIGNhc2UgJ1NZU1RFTV9DT01QTEVURV9XQVJNVVAnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIGJvdFN5c3RlbVN0YXR1czogJ1J1bm5pbmcnIH07XG4gICAgY2FzZSAnU1lTVEVNX1NUT1BfQk9UJzpcbiAgICAgIHJldHVybiB7IC4uLnN0YXRlLCBib3RTeXN0ZW1TdGF0dXM6ICdTdG9wcGVkJyB9O1xuICAgIGNhc2UgJ1NZU1RFTV9SRVNFVF9CT1QnOlxuICAgICAgLy8gRnJlc2ggU3RhcnQ6IENsZWFyIGFsbCB0YXJnZXQgcHJpY2Ugcm93cyBhbmQgb3JkZXIgaGlzdG9yeSBjb21wbGV0ZWx5XG4gICAgICBsYXN0QWN0aW9uVGltZXN0YW1wUGVyQ291bnRlci5jbGVhcigpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGJvdFN5c3RlbVN0YXR1czogJ1N0b3BwZWQnLFxuICAgICAgICB0YXJnZXRQcmljZVJvd3M6IFtdLCAvLyBDb21wbGV0ZWx5IGNsZWFyIGFsbCB0YXJnZXQgcHJpY2Ugcm93c1xuICAgICAgICBvcmRlckhpc3Rvcnk6IFtdLFxuICAgICAgfTtcbiAgICBjYXNlICdTRVRfVEFSR0VUX1BSSUNFX1JPV1MnOlxuICAgICAgcmV0dXJuIHsgLi4uc3RhdGUsIHRhcmdldFByaWNlUm93czogYWN0aW9uLnBheWxvYWQgfTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIHN0YXRlO1xuICB9XG59O1xuXG5pbnRlcmZhY2UgVHJhZGluZ0NvbnRleHRUeXBlIGV4dGVuZHMgT21pdDxUcmFkaW5nU3RhdGUsICdiYWNrZW5kU3RhdHVzJyB8ICdib3RTeXN0ZW1TdGF0dXMnPiB7XG4gIGRpc3BhdGNoOiBSZWFjdC5EaXNwYXRjaDxUcmFkaW5nQWN0aW9uPjtcbiAgc2V0VGFyZ2V0UHJpY2VzOiAocHJpY2VzOiBudW1iZXJbXSkgPT4gdm9pZDtcbiAgZ2V0RGlzcGxheU9yZGVyczogKCkgPT4gRGlzcGxheU9yZGVyUm93W107XG4gIGJhY2tlbmRTdGF0dXM6ICdvbmxpbmUnIHwgJ29mZmxpbmUnIHwgJ3Vua25vd24nO1xuICBmZXRjaE1hcmtldFByaWNlOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBjaGVja0JhY2tlbmRTdGF0dXM6ICgpID0+IFByb21pc2U8dm9pZD47XG4gIGJvdFN5c3RlbVN0YXR1czogQm90U3lzdGVtU3RhdHVzO1xuICBpc0JvdEFjdGl2ZTogYm9vbGVhbjsgLy8gRGVyaXZlZCBmcm9tIGJvdFN5c3RlbVN0YXR1c1xuICBzdGFydEJhY2tlbmRCb3Q6IChjb25maWdJZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xuICBzdG9wQmFja2VuZEJvdDogKGNvbmZpZ0lkOiBzdHJpbmcpID0+IFByb21pc2U8Ym9vbGVhbj47XG4gIHNhdmVDb25maWdUb0JhY2tlbmQ6IChjb25maWc6IFRyYWRpbmdDb25maWcpID0+IFByb21pc2U8c3RyaW5nIHwgbnVsbD47XG59XG5cbmNvbnN0IFRyYWRpbmdDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUcmFkaW5nQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbi8vIFNJTVBMSUZJRUQgTE9HSUMgLSBOTyBDT01QTEVYIENPT0xET1dOU1xuXG5leHBvcnQgY29uc3QgVHJhZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pID0+IHtcbiAgLy8gSW5pdGlhbGl6ZSBzdGF0ZSB3aXRoIGxvY2FsU3RvcmFnZSBkYXRhIGlmIGF2YWlsYWJsZVxuICBjb25zdCBpbml0aWFsaXplU3RhdGUgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRTdGF0ZSA9IGxvYWRTdGF0ZUZyb21Mb2NhbFN0b3JhZ2UoKTtcbiAgICBpZiAoc2F2ZWRTdGF0ZSkge1xuICAgICAgcmV0dXJuIHsgLi4uaW5pdGlhbFRyYWRpbmdTdGF0ZSwgLi4uc2F2ZWRTdGF0ZSB9O1xuICAgIH1cbiAgICByZXR1cm4gaW5pdGlhbFRyYWRpbmdTdGF0ZTtcbiAgfTtcblxuICBjb25zdCBbc3RhdGUsIGRpc3BhdGNoXSA9IHVzZVJlZHVjZXIodHJhZGluZ1JlZHVjZXIsIGluaXRpYWxpemVTdGF0ZSgpKTtcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKTtcbiAgY29uc3QgYXVkaW9SZWYgPSB1c2VSZWY8SFRNTEF1ZGlvRWxlbWVudCB8IG51bGw+KG51bGwpO1xuICAvLyBSZW1vdmVkIHByb2Nlc3NpbmcgbG9ja3MgYW5kIGNvb2xkb3ducyBmb3IgY29udGludW91cyB0cmFkaW5nXG5cbiAgLy8gSW5pdGlhbGl6ZSBmZXRjaE1hcmtldFByaWNlIGZpcnN0XG4gIGNvbnN0IGZldGNoTWFya2V0UHJpY2UgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIERvbid0IGZldGNoIHByaWNlIGlmIGVpdGhlciBjcnlwdG8gaXMgbm90IHNlbGVjdGVkXG4gICAgICBpZiAoIXN0YXRlLmNvbmZpZy5jcnlwdG8xIHx8ICFzdGF0ZS5jb25maWcuY3J5cHRvMikge1xuICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfTUFSS0VUX1BSSUNFJywgcGF5bG9hZDogMCB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgY29uc3QgcHJpY2UgPSBhd2FpdCBnZXRNYXJrZXRQcmljZUZyb21BUEkoc3RhdGUuY29uZmlnKTtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9NQVJLRVRfUFJJQ0UnLCBwYXlsb2FkOiBwcmljZSB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIG1hcmtldCBwcmljZTonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbc3RhdGUuY29uZmlnLCBkaXNwYXRjaF0pO1xuXG4gIC8vIE1hcmtldCBwcmljZSBmbHVjdHVhdGlvbiBlZmZlY3QgLSBzaW11bGF0ZXMgcmVhbC10aW1lIHByaWNlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsIGZldGNoXG4gICAgZmV0Y2hNYXJrZXRQcmljZSgpO1xuXG4gICAgLy8gU2V0IHVwIHByaWNlIGZsdWN0dWF0aW9uIGludGVydmFsIGZvciBzaW11bGF0aW9uXG4gICAgY29uc3QgcHJpY2VGbHVjdHVhdGlvbkludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnRkxVQ1RVQVRFX01BUktFVF9QUklDRScgfSk7XG4gICAgfSwgMjAwMCk7IC8vIFVwZGF0ZSBldmVyeSAyIHNlY29uZHMgZm9yIHJlYWxpc3RpYyBzaW11bGF0aW9uXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJJbnRlcnZhbChwcmljZUZsdWN0dWF0aW9uSW50ZXJ2YWwpO1xuICAgIH07XG4gIH0sIFtmZXRjaE1hcmtldFByaWNlLCBkaXNwYXRjaF0pO1xuXG4gIC8vIE90aGVyIGVmZmVjdHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICBhdWRpb1JlZi5jdXJyZW50ID0gbmV3IEF1ZGlvKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgcGxheVNvdW5kID0gdXNlQ2FsbGJhY2soKHNvdW5kS2V5OiAnc291bmRPcmRlckV4ZWN1dGlvbicgfCAnc291bmRFcnJvcicpID0+IHtcbiAgICBpZiAoc3RhdGUuYXBwU2V0dGluZ3Muc291bmRBbGVydHNFbmFibGVkICYmIGF1ZGlvUmVmLmN1cnJlbnQpIHtcbiAgICAgIGxldCBzb3VuZFBhdGg6IHN0cmluZyB8IHVuZGVmaW5lZDtcbiAgICAgIGlmIChzb3VuZEtleSA9PT0gJ3NvdW5kT3JkZXJFeGVjdXRpb24nICYmIHN0YXRlLmFwcFNldHRpbmdzLmFsZXJ0T25PcmRlckV4ZWN1dGlvbikge1xuICAgICAgICBzb3VuZFBhdGggPSBzdGF0ZS5hcHBTZXR0aW5ncy5zb3VuZE9yZGVyRXhlY3V0aW9uO1xuICAgICAgfSBlbHNlIGlmIChzb3VuZEtleSA9PT0gJ3NvdW5kRXJyb3InICYmIHN0YXRlLmFwcFNldHRpbmdzLmFsZXJ0T25FcnJvcikge1xuICAgICAgICBzb3VuZFBhdGggPSBzdGF0ZS5hcHBTZXR0aW5ncy5zb3VuZEVycm9yO1xuICAgICAgfVxuXG4gICAgICBpZiAoc291bmRQYXRoKSB7XG4gICAgICAgIGF1ZGlvUmVmLmN1cnJlbnQuc3JjID0gc291bmRQYXRoO1xuICAgICAgICBhdWRpb1JlZi5jdXJyZW50LmN1cnJlbnRUaW1lID0gMDsgLy8gUmVzZXQgdG8gYmVnaW5uaW5nXG5cbiAgICAgICAgLy8gUGxheSB0aGUgc291bmQgYW5kIGxpbWl0IGR1cmF0aW9uIHRvIDIgc2Vjb25kc1xuICAgICAgICBhdWRpb1JlZi5jdXJyZW50LnBsYXkoKS50aGVuKCgpID0+IHtcbiAgICAgICAgICAvLyBTZXQgYSB0aW1lb3V0IHRvIHBhdXNlIHRoZSBhdWRpbyBhZnRlciAyIHNlY29uZHNcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGlmIChhdWRpb1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgIGF1ZGlvUmVmLmN1cnJlbnQucGF1c2UoKTtcbiAgICAgICAgICAgICAgYXVkaW9SZWYuY3VycmVudC5jdXJyZW50VGltZSA9IDA7IC8vIFJlc2V0IGZvciBuZXh0IHBsYXlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LCAyMDAwKTsgLy8gMiBzZWNvbmRzXG4gICAgICAgIH0pLmNhdGNoKGVyciA9PiBjb25zb2xlLmVycm9yKFwiRXJyb3IgcGxheWluZyBzb3VuZDpcIiwgZXJyKSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGUuYXBwU2V0dGluZ3NdKTtcblxuICAvLyBUZWxlZ3JhbSBub3RpZmljYXRpb24gZnVuY3Rpb25cbiAgY29uc3Qgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKG1lc3NhZ2U6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0ZWxlZ3JhbVRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RlbGVncmFtX2JvdF90b2tlbicpO1xuICAgICAgY29uc3QgdGVsZWdyYW1DaGF0SWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGVsZWdyYW1fY2hhdF9pZCcpO1xuXG4gICAgICBpZiAoIXRlbGVncmFtVG9rZW4gfHwgIXRlbGVncmFtQ2hhdElkKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdUZWxlZ3JhbSBub3QgY29uZmlndXJlZCAtIHNraXBwaW5nIG5vdGlmaWNhdGlvbicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHBzOi8vYXBpLnRlbGVncmFtLm9yZy9ib3Qke3RlbGVncmFtVG9rZW59L3NlbmRNZXNzYWdlYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBjaGF0X2lkOiB0ZWxlZ3JhbUNoYXRJZCxcbiAgICAgICAgICB0ZXh0OiBtZXNzYWdlLFxuICAgICAgICAgIHBhcnNlX21vZGU6ICdIVE1MJ1xuICAgICAgICB9KVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgVGVsZWdyYW0gbm90aWZpY2F0aW9uOicsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIFRlbGVncmFtIG5vdGlmaWNhdGlvbjonLCBlcnJvcik7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gRWZmZWN0IHRvIHVwZGF0ZSBtYXJrZXQgcHJpY2Ugd2hlbiB0cmFkaW5nIHBhaXIgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFdoZW4gY3J5cHRvMSBvciBjcnlwdG8yIChwYXJ0cyBvZiBzdGF0ZS5jb25maWcpIGNoYW5nZSxcbiAgICAvLyB0aGUgZmV0Y2hNYXJrZXRQcmljZSB1c2VDYWxsYmFjayBnZXRzIGEgbmV3IHJlZmVyZW5jZS5cbiAgICAvLyBUaGUgdXNlRWZmZWN0IGFib3ZlICh3aGljaCBkZXBlbmRzIG9uIGZldGNoTWFya2V0UHJpY2UpXG4gICAgLy8gd2lsbCByZS1ydW4sIGNsZWFyIHRoZSBvbGQgaW50ZXJ2YWwsIG1ha2UgYW4gaW5pdGlhbCBmZXRjaCB3aXRoIHRoZSBuZXcgY29uZmlnLFxuICAgIC8vIGFuZCBzZXQgdXAgYSBuZXcgaW50ZXJ2YWwuXG4gICAgLy8gVGhlIHJlZHVjZXIgZm9yIFNFVF9DT05GSUcgYWxzbyBzZXRzIGFuIGluaXRpYWwgbWFya2V0IHByaWNlIGlmIGNyeXB0bzEvY3J5cHRvMiBjaGFuZ2VzLlxuICAgIC8vIFRodXMsIG5vIGV4cGxpY2l0IGRpc3BhdGNoIGlzIG5lZWRlZCBoZXJlLlxuICB9LCBbc3RhdGUuY29uZmlnLmNyeXB0bzEsIHN0YXRlLmNvbmZpZy5jcnlwdG8yXSk7IC8vIERlcGVuZGVuY2llcyBlbnN1cmUgdGhpcyByZWFjdHMgdG8gcGFpciBjaGFuZ2VzXG5cbiAgY29uc3Qgc2V0VGFyZ2V0UHJpY2VzID0gdXNlQ2FsbGJhY2soKHByaWNlczogbnVtYmVyW10pID0+IHtcbiAgICBpZiAoIXByaWNlcyB8fCAhQXJyYXkuaXNBcnJheShwcmljZXMpKSByZXR1cm47XG5cbiAgICAvLyBTb3J0IHByaWNlcyBmcm9tIGxvd2VzdCB0byBoaWdoZXN0IGZvciBwcm9wZXIgY291bnRlciBhc3NpZ25tZW50XG4gICAgY29uc3Qgc29ydGVkUHJpY2VzID0gWy4uLnByaWNlc10uZmlsdGVyKChwcmljZTogbnVtYmVyKSA9PiAhaXNOYU4ocHJpY2UpICYmIHByaWNlID4gMCkuc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuXG4gICAgY29uc3QgbmV3Um93czogVGFyZ2V0UHJpY2VSb3dbXSA9IHNvcnRlZFByaWNlcy5tYXAoKHByaWNlOiBudW1iZXIsIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdSb3cgPSBzdGF0ZS50YXJnZXRQcmljZVJvd3MuZmluZCgocjogVGFyZ2V0UHJpY2VSb3cpID0+IHIudGFyZ2V0UHJpY2UgPT09IHByaWNlKTtcbiAgICAgICAgaWYgKGV4aXN0aW5nUm93KSB7XG4gICAgICAgICAgLy8gVXBkYXRlIGNvdW50ZXIgZm9yIGV4aXN0aW5nIHJvdyBiYXNlZCBvbiBzb3J0ZWQgcG9zaXRpb25cbiAgICAgICAgICByZXR1cm4geyAuLi5leGlzdGluZ1JvdywgY291bnRlcjogaW5kZXggKyAxIH07XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICBjb3VudGVyOiBpbmRleCArIDEsIC8vIENvdW50ZXIgc3RhcnRzIGZyb20gMSwgbG93ZXN0IHByaWNlIGdldHMgY291bnRlciAxXG4gICAgICAgICAgc3RhdHVzOiAnRnJlZScgYXMgT3JkZXJTdGF0dXMsXG4gICAgICAgICAgb3JkZXJMZXZlbDogMCxcbiAgICAgICAgICB2YWx1ZUxldmVsOiBzdGF0ZS5jb25maWcuYmFzZUJpZCwgLy8gVXNlIGJhc2VCaWQgZnJvbSBjb25maWdcbiAgICAgICAgICB0YXJnZXRQcmljZTogcHJpY2UsXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfVEFSR0VUX1BSSUNFX1JPV1MnLCBwYXlsb2FkOiBuZXdSb3dzIH0pO1xuICB9LCBbc3RhdGUudGFyZ2V0UHJpY2VSb3dzLCBzdGF0ZS5jb25maWcuYmFzZUJpZCwgZGlzcGF0Y2hdKTtcblxuICAvLyBDb3JlIFRyYWRpbmcgTG9naWMgKFNpbXVsYXRlZCkgLSBDT05USU5VT1VTIFRSQURJTkcgVkVSU0lPTlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIE9ubHkgY2hlY2sgZXNzZW50aWFsIGNvbmRpdGlvbnNcbiAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzICE9PSAnUnVubmluZycgfHwgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLmxlbmd0aCA9PT0gMCB8fCBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UgPD0gMCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEV4ZWN1dGUgdHJhZGluZyBsb2dpYyBpbW1lZGlhdGVseSAtIG5vIGxvY2tzLCBubyBjb29sZG93bnMsIG5vIGRlbGF5c1xuICAgIGNvbnN0IHsgY29uZmlnLCBjdXJyZW50TWFya2V0UHJpY2UsIHRhcmdldFByaWNlUm93cywgY3J5cHRvMUJhbGFuY2UsIGNyeXB0bzJCYWxhbmNlIH0gPSBzdGF0ZTtcbiAgICBjb25zdCBzb3J0ZWRSb3dzRm9yTG9naWMgPSBbLi4udGFyZ2V0UHJpY2VSb3dzXS5zb3J0KChhOiBUYXJnZXRQcmljZVJvdywgYjogVGFyZ2V0UHJpY2VSb3cpID0+IGEudGFyZ2V0UHJpY2UgLSBiLnRhcmdldFByaWNlKTtcblxuICAgIC8vIFVzZSBtdXRhYmxlIHZhcmlhYmxlcyBmb3IgYmFsYW5jZSB0cmFja2luZyB3aXRoaW4gdGhpcyBjeWNsZVxuICAgIGxldCBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgPSBjcnlwdG8xQmFsYW5jZTtcbiAgICBsZXQgY3VycmVudENyeXB0bzJCYWxhbmNlID0gY3J5cHRvMkJhbGFuY2U7XG4gICAgbGV0IGFjdGlvbnNUYWtlbiA9IDA7XG5cbiAgICBjb25zb2xlLmxvZyhg8J+agCBDT05USU5VT1VTIFRSQURJTkc6IFByaWNlICQke2N1cnJlbnRNYXJrZXRQcmljZS50b0ZpeGVkKDIpfSB8IFRhcmdldHM6ICR7c29ydGVkUm93c0ZvckxvZ2ljLmxlbmd0aH0gfCBCYWxhbmNlOiAkJHtjdXJyZW50Q3J5cHRvMkJhbGFuY2V9ICR7Y29uZmlnLmNyeXB0bzJ9YCk7XG5cbiAgICAvLyBTaG93IHdoaWNoIHRhcmdldHMgYXJlIGluIHJhbmdlXG4gICAgY29uc3QgdGFyZ2V0c0luUmFuZ2UgPSBzb3J0ZWRSb3dzRm9yTG9naWMuZmlsdGVyKHJvdyA9PiB7XG4gICAgICBjb25zdCBkaWZmUGVyY2VudCA9IChNYXRoLmFicyhjdXJyZW50TWFya2V0UHJpY2UgLSByb3cudGFyZ2V0UHJpY2UpIC8gY3VycmVudE1hcmtldFByaWNlKSAqIDEwMDtcbiAgICAgIHJldHVybiBkaWZmUGVyY2VudCA8PSBjb25maWcuc2xpcHBhZ2VQZXJjZW50O1xuICAgIH0pO1xuXG4gICAgaWYgKHRhcmdldHNJblJhbmdlLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn46vIFRBUkdFVFMgSU4gUkFOR0UgKMKxJHtjb25maWcuc2xpcHBhZ2VQZXJjZW50fSUpOmAsIHRhcmdldHNJblJhbmdlLm1hcChyb3cgPT4gYENvdW50ZXIgJHtyb3cuY291bnRlcn0gKCR7cm93LnN0YXR1c30pYCkpO1xuICAgIH1cblxuICAgIC8vIENPTlRJTlVPVVMgVFJBRElORyBMT0dJQzogUHJvY2VzcyBhbGwgdGFyZ2V0cyBpbW1lZGlhdGVseVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc29ydGVkUm93c0ZvckxvZ2ljLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBhY3RpdmVSb3cgPSBzb3J0ZWRSb3dzRm9yTG9naWNbaV07XG4gICAgICBjb25zdCBwcmljZURpZmZQZXJjZW50ID0gTWF0aC5hYnMoY3VycmVudE1hcmtldFByaWNlIC0gYWN0aXZlUm93LnRhcmdldFByaWNlKSAvIGN1cnJlbnRNYXJrZXRQcmljZSAqIDEwMDtcblxuICAgICAgLy8gU1RFUCAxOiBDaGVjayBpZiBUYXJnZXRSb3dOIGlzIHRyaWdnZXJlZCAod2l0aGluIHNsaXBwYWdlIHJhbmdlKVxuICAgICAgaWYgKHByaWNlRGlmZlBlcmNlbnQgPD0gY29uZmlnLnNsaXBwYWdlUGVyY2VudCkge1xuXG4gICAgICAgIGlmIChjb25maWcudHJhZGluZ01vZGUgPT09IFwiU2ltcGxlU3BvdFwiKSB7XG4gICAgICAgICAgLy8gU1RFUCAyOiBFdmFsdWF0ZSBUcmlnZ2VyZWQgVGFyZ2V0Um93TidzIFN0YXR1c1xuICAgICAgICAgIGlmIChhY3RpdmVSb3cuc3RhdHVzID09PSBcIkZyZWVcIikge1xuICAgICAgICAgICAgLy8gU1RFUCAyYTogRXhlY3V0ZSBCVVkgb24gVGFyZ2V0Um93TlxuICAgICAgICAgICAgY29uc3QgY29zdENyeXB0bzIgPSBhY3RpdmVSb3cudmFsdWVMZXZlbDtcblxuICAgICAgICAgICAgaWYgKGN1cnJlbnRDcnlwdG8yQmFsYW5jZSA+PSBjb3N0Q3J5cHRvMikge1xuICAgICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8xQm91Z2h0ID0gY29zdENyeXB0bzIgLyBjdXJyZW50TWFya2V0UHJpY2U7XG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRSb3c6IFRhcmdldFByaWNlUm93ID0ge1xuICAgICAgICAgICAgICAgIC4uLmFjdGl2ZVJvdyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6IFwiRnVsbFwiLFxuICAgICAgICAgICAgICAgIG9yZGVyTGV2ZWw6IGFjdGl2ZVJvdy5vcmRlckxldmVsICsgMSxcbiAgICAgICAgICAgICAgICB2YWx1ZUxldmVsOiBjb25maWcuYmFzZUJpZCAqIE1hdGgucG93KGNvbmZpZy5tdWx0aXBsaWVyLCBhY3RpdmVSb3cub3JkZXJMZXZlbCArIDEpLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFBbW91bnRIZWxkOiBhbW91bnRDcnlwdG8xQm91Z2h0LFxuICAgICAgICAgICAgICAgIG9yaWdpbmFsQ29zdENyeXB0bzI6IGNvc3RDcnlwdG8yLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFWYXI6IGFtb3VudENyeXB0bzFCb3VnaHQsXG4gICAgICAgICAgICAgICAgY3J5cHRvMlZhcjogLWNvc3RDcnlwdG8yLFxuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICBkaXNwYXRjaCh7IHR5cGU6ICdVUERBVEVfVEFSR0VUX1BSSUNFX1JPVycsIHBheWxvYWQ6IHVwZGF0ZWRSb3cgfSk7XG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9CQUxBTkNFUycsIHBheWxvYWQ6IHsgY3J5cHRvMTogY3VycmVudENyeXB0bzFCYWxhbmNlICsgYW1vdW50Q3J5cHRvMUJvdWdodCwgY3J5cHRvMjogY3VycmVudENyeXB0bzJCYWxhbmNlIC0gY29zdENyeXB0bzIgfSB9KTtcbiAgICAgICAgICAgICAgZGlzcGF0Y2goe1xuICAgICAgICAgICAgICAgIHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWScsXG4gICAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgICAgaWQ6IHV1aWR2NCgpLFxuICAgICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9YCxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8xLFxuICAgICAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIkJVWVwiLFxuICAgICAgICAgICAgICAgICAgYW1vdW50Q3J5cHRvMTogYW1vdW50Q3J5cHRvMUJvdWdodCxcbiAgICAgICAgICAgICAgICAgIGF2Z1ByaWNlOiBjdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IGNvc3RDcnlwdG8yLFxuICAgICAgICAgICAgICAgICAgcHJpY2UxOiBjdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8xU3ltYm9sOiBjb25maWcuY3J5cHRvMSB8fCAnJyxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5jcnlwdG8yIHx8ICcnXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBCVVk6IENvdW50ZXIgJHthY3RpdmVSb3cuY291bnRlcn0gYm91Z2h0ICR7YW1vdW50Q3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSBhdCAkJHtjdXJyZW50TWFya2V0UHJpY2UudG9GaXhlZCgyKX1gKTtcbiAgICAgICAgICAgICAgdG9hc3QoeyB0aXRsZTogXCJCVVkgRXhlY3V0ZWRcIiwgZGVzY3JpcHRpb246IGBDb3VudGVyICR7YWN0aXZlUm93LmNvdW50ZXJ9OiAke2Ftb3VudENyeXB0bzFCb3VnaHQudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX1gLCBkdXJhdGlvbjogMjAwMCB9KTtcbiAgICAgICAgICAgICAgcGxheVNvdW5kKCdzb3VuZE9yZGVyRXhlY3V0aW9uJyk7XG5cbiAgICAgICAgICAgICAgLy8gU2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb24gZm9yIEJVWVxuICAgICAgICAgICAgICBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24oXG4gICAgICAgICAgICAgICAgYPCfn6IgPGI+QlVZIEVYRUNVVEVEPC9iPlxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5OKIENvdW50ZXI6ICR7YWN0aXZlUm93LmNvdW50ZXJ9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfkrAgQW1vdW50OiAke2Ftb3VudENyeXB0bzFCb3VnaHQudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+StSBQcmljZTogJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfkrggQ29zdDogJCR7Y29zdENyeXB0bzIudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+TiCBNb2RlOiBTaW1wbGUgU3BvdGBcbiAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICBhY3Rpb25zVGFrZW4rKztcblxuICAgICAgICAgICAgICAvLyBVcGRhdGUgbG9jYWwgYmFsYW5jZSBmb3Igc3Vic2VxdWVudCBjaGVja3MgaW4gdGhpcyBjeWNsZVxuICAgICAgICAgICAgICBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLT0gY29zdENyeXB0bzI7XG4gICAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8xQmFsYW5jZSArPSBhbW91bnRDcnlwdG8xQm91Z2h0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNURVAgMzogQ2hlY2sgYW5kIFByb2Nlc3MgSW5mZXJpb3IgVGFyZ2V0Um93IChUYXJnZXRSb3dOX21pbnVzXzEpIC0gQUxXQVlTLCByZWdhcmRsZXNzIG9mIEJVWVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRDb3VudGVyID0gYWN0aXZlUm93LmNvdW50ZXI7XG4gICAgICAgICAgY29uc3QgaW5mZXJpb3JSb3cgPSBzb3J0ZWRSb3dzRm9yTG9naWMuZmluZChyb3cgPT4gcm93LmNvdW50ZXIgPT09IGN1cnJlbnRDb3VudGVyIC0gMSk7XG5cbiAgICAgICAgICBpZiAoaW5mZXJpb3JSb3cgJiYgaW5mZXJpb3JSb3cuc3RhdHVzID09PSBcIkZ1bGxcIiAmJiBpbmZlcmlvclJvdy5jcnlwdG8xQW1vdW50SGVsZCAmJiBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8xVG9TZWxsID0gaW5mZXJpb3JSb3cuY3J5cHRvMUFtb3VudEhlbGQ7XG4gICAgICAgICAgICBjb25zdCBjcnlwdG8yUmVjZWl2ZWQgPSBhbW91bnRDcnlwdG8xVG9TZWxsICogY3VycmVudE1hcmtldFByaWNlO1xuICAgICAgICAgICAgY29uc3QgcmVhbGl6ZWRQcm9maXQgPSBjcnlwdG8yUmVjZWl2ZWQgLSBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yO1xuXG4gICAgICAgICAgICAvLyBDYWxjdWxhdGUgQ3J5cHRvMSBwcm9maXQvbG9zcyBiYXNlZCBvbiBpbmNvbWUgc3BsaXQgcGVyY2VudGFnZVxuICAgICAgICAgICAgY29uc3QgcmVhbGl6ZWRQcm9maXRDcnlwdG8xID0gY3VycmVudE1hcmtldFByaWNlID4gMCA/IChyZWFsaXplZFByb2ZpdCAqIGNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzFQZXJjZW50IC8gMTAwKSAvIGN1cnJlbnRNYXJrZXRQcmljZSA6IDA7XG5cbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRJbmZlcmlvclJvdzogVGFyZ2V0UHJpY2VSb3cgPSB7XG4gICAgICAgICAgICAgIC4uLmluZmVyaW9yUm93LFxuICAgICAgICAgICAgICBzdGF0dXM6IFwiRnJlZVwiLFxuICAgICAgICAgICAgICBjcnlwdG8xQW1vdW50SGVsZDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGluZmVyaW9yUm93Lm9yZGVyTGV2ZWwpLFxuICAgICAgICAgICAgICBjcnlwdG8xVmFyOiAtYW1vdW50Q3J5cHRvMVRvU2VsbCxcbiAgICAgICAgICAgICAgY3J5cHRvMlZhcjogY3J5cHRvMlJlY2VpdmVkLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZEluZmVyaW9yUm93IH0pO1xuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLSBhbW91bnRDcnlwdG8xVG9TZWxsLCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKyBjcnlwdG8yUmVjZWl2ZWQgfSB9KTtcbiAgICAgICAgICAgIGRpc3BhdGNoKHtcbiAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgIGlkOiB1dWlkdjQoKSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9YCxcbiAgICAgICAgICAgICAgICBjcnlwdG8xOiBjb25maWcuY3J5cHRvMSxcbiAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiU0VMTFwiLFxuICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGFtb3VudENyeXB0bzFUb1NlbGwsXG4gICAgICAgICAgICAgICAgYXZnUHJpY2U6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IGNyeXB0bzJSZWNlaXZlZCxcbiAgICAgICAgICAgICAgICBwcmljZTE6IGN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICAgICAgICBjcnlwdG8xU3ltYm9sOiBjb25maWcuY3J5cHRvMSB8fCAnJyxcbiAgICAgICAgICAgICAgICBjcnlwdG8yU3ltYm9sOiBjb25maWcuY3J5cHRvMiB8fCAnJyxcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yOiByZWFsaXplZFByb2ZpdCxcbiAgICAgICAgICAgICAgICByZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xOiByZWFsaXplZFByb2ZpdENyeXB0bzFcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFNFTEw6IENvdW50ZXIgJHtjdXJyZW50Q291bnRlciAtIDF9IHNvbGQgJHthbW91bnRDcnlwdG8xVG9TZWxsLnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9LiBQcm9maXQ6ICQke3JlYWxpemVkUHJvZml0LnRvRml4ZWQoMil9YCk7XG4gICAgICAgICAgICB0b2FzdCh7IHRpdGxlOiBcIlNFTEwgRXhlY3V0ZWRcIiwgZGVzY3JpcHRpb246IGBDb3VudGVyICR7Y3VycmVudENvdW50ZXIgLSAxfTogUHJvZml0ICQke3JlYWxpemVkUHJvZml0LnRvRml4ZWQoMil9YCwgZHVyYXRpb246IDIwMDAgfSk7XG4gICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgLy8gU2VuZCBUZWxlZ3JhbSBub3RpZmljYXRpb24gZm9yIFNFTExcbiAgICAgICAgICAgIGNvbnN0IHByb2ZpdEVtb2ppID0gcmVhbGl6ZWRQcm9maXQgPiAwID8gJ/Cfk4gnIDogcmVhbGl6ZWRQcm9maXQgPCAwID8gJ/Cfk4knIDogJ+Kelic7XG4gICAgICAgICAgICBzZW5kVGVsZWdyYW1Ob3RpZmljYXRpb24oXG4gICAgICAgICAgICAgIGDwn5S0IDxiPlNFTEwgRVhFQ1VURUQ8L2I+XFxuYCArXG4gICAgICAgICAgICAgIGDwn5OKIENvdW50ZXI6ICR7Y3VycmVudENvdW50ZXIgLSAxfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+SsCBBbW91bnQ6ICR7YW1vdW50Q3J5cHRvMVRvU2VsbC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfVxcbmAgK1xuICAgICAgICAgICAgICBg8J+StSBQcmljZTogJCR7Y3VycmVudE1hcmtldFByaWNlLnRvRml4ZWQoMil9XFxuYCArXG4gICAgICAgICAgICAgIGDwn5K4IFJlY2VpdmVkOiAkJHtjcnlwdG8yUmVjZWl2ZWQudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgYCR7cHJvZml0RW1vaml9IFByb2ZpdDogJCR7cmVhbGl6ZWRQcm9maXQudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgYPCfk4ggTW9kZTogU2ltcGxlIFNwb3RgXG4gICAgICAgICAgICApO1xuXG4gICAgICAgICAgICBhY3Rpb25zVGFrZW4rKztcblxuICAgICAgICAgICAgLy8gVXBkYXRlIGxvY2FsIGJhbGFuY2UgZm9yIHN1YnNlcXVlbnQgY2hlY2tzIGluIHRoaXMgY3ljbGVcbiAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8xQmFsYW5jZSAtPSBhbW91bnRDcnlwdG8xVG9TZWxsO1xuICAgICAgICAgICAgY3VycmVudENyeXB0bzJCYWxhbmNlICs9IGNyeXB0bzJSZWNlaXZlZDtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoY29uZmlnLnRyYWRpbmdNb2RlID09PSBcIlN0YWJsZWNvaW5Td2FwXCIpIHtcbiAgICAgICAgICAvLyBTVEVQIDI6IEV2YWx1YXRlIFRyaWdnZXJlZCBUYXJnZXRSb3dOJ3MgU3RhdHVzIChTdGFibGVjb2luIFN3YXAgTW9kZSlcbiAgICAgICAgICBpZiAoYWN0aXZlUm93LnN0YXR1cyA9PT0gXCJGcmVlXCIpIHtcbiAgICAgICAgICAgIC8vIFNURVAgMmE6IEV4ZWN1dGUgVHdvLVN0ZXAgXCJCdXkgQ3J5cHRvMSB2aWEgU3RhYmxlY29pblwiIG9uIFRhcmdldFJvd05cbiAgICAgICAgICAgIGNvbnN0IGFtb3VudENyeXB0bzJUb1VzZSA9IGFjdGl2ZVJvdy52YWx1ZUxldmVsOyAvLyBWYWx1ZSA9IEJhc2VCaWQgKiAoTXVsdGlwbGllciBeIExldmVsKVxuXG4gICAgICAgICAgICBpZiAoY3VycmVudENyeXB0bzJCYWxhbmNlID49IGFtb3VudENyeXB0bzJUb1VzZSkge1xuICAgICAgICAgICAgICAvLyBTdGVwIDE6IFNlbGwgQ3J5cHRvMiBmb3IgUHJlZmVycmVkU3RhYmxlY29pblxuICAgICAgICAgICAgICAvLyBHZXQgcmVhbCBtYXJrZXQgcHJpY2UgZm9yIENyeXB0bzIvU3RhYmxlY29pbiBwYWlyIChzeW5jaHJvbm91cyBmb3Igbm93KVxuICAgICAgICAgICAgICBjb25zdCBjcnlwdG8yU3RhYmxlY29pblByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzIgfHwgJ1VTRFQnKSAvIGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICAgIGNvbnN0IHN0YWJsZWNvaW5PYnRhaW5lZCA9IGFtb3VudENyeXB0bzJUb1VzZSAqIGNyeXB0bzJTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgICAgLy8gU3RlcCAyOiBCdXkgQ3J5cHRvMSB3aXRoIFN0YWJsZWNvaW5cbiAgICAgICAgICAgICAgLy8gR2V0IHJlYWwgbWFya2V0IHByaWNlIGZvciBDcnlwdG8xL1N0YWJsZWNvaW4gcGFpclxuICAgICAgICAgICAgICBjb25zdCBjcnlwdG8xU3RhYmxlY29pblByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzEgfHwgJ0JUQycpIC8gZ2V0VVNEUHJpY2UoY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJ1VTRFQnKTtcbiAgICAgICAgICAgICAgY29uc3QgY3J5cHRvMUJvdWdodCA9IHN0YWJsZWNvaW5PYnRhaW5lZCAvIGNyeXB0bzFTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgICAgLy8gVXBkYXRlIFJvdyBOOiBGcmVlIOKGkiBGdWxsLCBMZXZlbCsrLCBWYWx1ZSByZWNhbGN1bGF0ZWRcbiAgICAgICAgICAgICAgY29uc3QgbmV3TGV2ZWwgPSBhY3RpdmVSb3cub3JkZXJMZXZlbCArIDE7XG4gICAgICAgICAgICAgIGNvbnN0IG5ld1ZhbHVlID0gY29uZmlnLmJhc2VCaWQgKiBNYXRoLnBvdyhjb25maWcubXVsdGlwbGllciwgbmV3TGV2ZWwpO1xuXG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRSb3c6IFRhcmdldFByaWNlUm93ID0ge1xuICAgICAgICAgICAgICAgIC4uLmFjdGl2ZVJvdyxcbiAgICAgICAgICAgICAgICBzdGF0dXM6IFwiRnVsbFwiLCAvLyBGcmVlIOKGkiBGdWxsXG4gICAgICAgICAgICAgICAgb3JkZXJMZXZlbDogbmV3TGV2ZWwsIC8vIExldmVsID0gTGV2ZWwgKyAxXG4gICAgICAgICAgICAgICAgdmFsdWVMZXZlbDogbmV3VmFsdWUsIC8vIFZhbHVlID0gQmFzZUJpZCAqIChNdWx0aXBsaWVyIF4gTGV2ZWwpXG4gICAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IGNyeXB0bzFCb3VnaHQsIC8vIFN0b3JlIGFtb3VudCBoZWxkIGZvciBOLTEgbG9naWNcbiAgICAgICAgICAgICAgICBvcmlnaW5hbENvc3RDcnlwdG8yOiBhbW91bnRDcnlwdG8yVG9Vc2UsIC8vIFN0b3JlIG9yaWdpbmFsIGNvc3QgZm9yIHByb2ZpdCBjYWxjdWxhdGlvblxuICAgICAgICAgICAgICAgIGNyeXB0bzFWYXI6IGNyeXB0bzFCb3VnaHQsIC8vIFBvc2l0aXZlIGFtb3VudCBvZiBDcnlwdG8xIGFjcXVpcmVkXG4gICAgICAgICAgICAgICAgY3J5cHRvMlZhcjogLWFtb3VudENyeXB0bzJUb1VzZSwgLy8gTmVnYXRpdmUgYW1vdW50IG9mIENyeXB0bzIgc29sZFxuICAgICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZFJvdyB9KTtcbiAgICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgKyBjcnlwdG8xQm91Z2h0LCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgLSBhbW91bnRDcnlwdG8yVG9Vc2UgfSB9KTtcblxuICAgICAgICAgICAgICAvLyBBZGQgaGlzdG9yeSBlbnRyaWVzIGZvciBib3RoIHN0ZXBzIG9mIHRoZSBzdGFibGVjb2luIHN3YXBcbiAgICAgICAgICAgICAgZGlzcGF0Y2goe1xuICAgICAgICAgICAgICAgIHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWScsXG4gICAgICAgICAgICAgICAgcGF5bG9hZDoge1xuICAgICAgICAgICAgICAgICAgaWQ6IHV1aWR2NCgpLFxuICAgICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgICAgcGFpcjogYCR7Y29uZmlnLmNyeXB0bzJ9LyR7Y29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW59YCxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8yLFxuICAgICAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIlNFTExcIixcbiAgICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGFtb3VudENyeXB0bzJUb1VzZSxcbiAgICAgICAgICAgICAgICAgIGF2Z1ByaWNlOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgICAgdmFsdWVDcnlwdG8yOiBzdGFibGVjb2luT2J0YWluZWQsXG4gICAgICAgICAgICAgICAgICBwcmljZTE6IGNyeXB0bzJTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgICBjcnlwdG8xU3ltYm9sOiBjb25maWcuY3J5cHRvMiB8fCAnJyxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICcnXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ0FERF9PUkRFUl9ISVNUT1JZX0VOVFJZJyxcbiAgICAgICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgICAgICAgICBwYWlyOiBgJHtjb25maWcuY3J5cHRvMX0vJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1gLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzEsXG4gICAgICAgICAgICAgICAgICBvcmRlclR5cGU6IFwiQlVZXCIsXG4gICAgICAgICAgICAgICAgICBhbW91bnRDcnlwdG8xOiBjcnlwdG8xQm91Z2h0LFxuICAgICAgICAgICAgICAgICAgYXZnUHJpY2U6IGNyeXB0bzFTdGFibGVjb2luUHJpY2UsXG4gICAgICAgICAgICAgICAgICB2YWx1ZUNyeXB0bzI6IHN0YWJsZWNvaW5PYnRhaW5lZCxcbiAgICAgICAgICAgICAgICAgIHByaWNlMTogY3J5cHRvMVN0YWJsZWNvaW5QcmljZSxcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8xIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMlN5bWJvbDogY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgU1RBQkxFQ09JTiBCVVk6IENvdW50ZXIgJHthY3RpdmVSb3cuY291bnRlcn0gfCBTdGVwIDE6IFNvbGQgJHthbW91bnRDcnlwdG8yVG9Vc2V9ICR7Y29uZmlnLmNyeXB0bzJ9IOKGkiAke3N0YWJsZWNvaW5PYnRhaW5lZC50b0ZpeGVkKDIpfSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufSB8IFN0ZXAgMjogQm91Z2h0ICR7Y3J5cHRvMUJvdWdodC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSB8IExldmVsOiAke2FjdGl2ZVJvdy5vcmRlckxldmVsfSDihpIgJHtuZXdMZXZlbH1gKTtcbiAgICAgICAgICAgICAgdG9hc3QoeyB0aXRsZTogXCJCVVkgRXhlY3V0ZWQgKFN0YWJsZWNvaW4pXCIsIGRlc2NyaXB0aW9uOiBgQ291bnRlciAke2FjdGl2ZVJvdy5jb3VudGVyfTogJHtjcnlwdG8xQm91Z2h0LnRvRml4ZWQoNil9ICR7Y29uZmlnLmNyeXB0bzF9IHZpYSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsIGR1cmF0aW9uOiAyMDAwIH0pO1xuICAgICAgICAgICAgICBwbGF5U291bmQoJ3NvdW5kT3JkZXJFeGVjdXRpb24nKTtcblxuICAgICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgU3RhYmxlY29pbiBCVVlcbiAgICAgICAgICAgICAgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKFxuICAgICAgICAgICAgICAgIGDwn5+iIDxiPkJVWSBFWEVDVVRFRCAoU3RhYmxlY29pbiBTd2FwKTwvYj5cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+TiiBDb3VudGVyOiAke2FjdGl2ZVJvdy5jb3VudGVyfVxcbmAgK1xuICAgICAgICAgICAgICAgIGDwn5SEIFN0ZXAgMTogU29sZCAke2Ftb3VudENyeXB0bzJUb1VzZS50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfSDihpIgJHtzdGFibGVjb2luT2J0YWluZWQudG9GaXhlZCgyKX0gJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+UhCBTdGVwIDI6IEJvdWdodCAke2NyeXB0bzFCb3VnaHQudG9GaXhlZCg2KX0gJHtjb25maWcuY3J5cHRvMX1cXG5gICtcbiAgICAgICAgICAgICAgICBg8J+TiiBMZXZlbDogJHthY3RpdmVSb3cub3JkZXJMZXZlbH0g4oaSICR7bmV3TGV2ZWx9XFxuYCArXG4gICAgICAgICAgICAgICAgYPCfk4ggTW9kZTogU3RhYmxlY29pbiBTd2FwYFxuICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgIGFjdGlvbnNUYWtlbisrO1xuXG4gICAgICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBiYWxhbmNlIGZvciBzdWJzZXF1ZW50IGNoZWNrcyBpbiB0aGlzIGN5Y2xlXG4gICAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8yQmFsYW5jZSAtPSBhbW91bnRDcnlwdG8yVG9Vc2U7XG4gICAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8xQmFsYW5jZSArPSBjcnlwdG8xQm91Z2h0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNURVAgMzogQ2hlY2sgYW5kIFByb2Nlc3MgSW5mZXJpb3IgVGFyZ2V0Um93IChUYXJnZXRSb3dOX21pbnVzXzEpIC0gQUxXQVlTLCByZWdhcmRsZXNzIG9mIEJVWVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRDb3VudGVyID0gYWN0aXZlUm93LmNvdW50ZXI7XG4gICAgICAgICAgY29uc3QgaW5mZXJpb3JSb3cgPSBzb3J0ZWRSb3dzRm9yTG9naWMuZmluZChyb3cgPT4gcm93LmNvdW50ZXIgPT09IGN1cnJlbnRDb3VudGVyIC0gMSk7XG5cbiAgICAgICAgICBpZiAoaW5mZXJpb3JSb3cgJiYgaW5mZXJpb3JSb3cuc3RhdHVzID09PSBcIkZ1bGxcIiAmJiBpbmZlcmlvclJvdy5jcnlwdG8xQW1vdW50SGVsZCAmJiBpbmZlcmlvclJvdy5vcmlnaW5hbENvc3RDcnlwdG8yKSB7XG4gICAgICAgICAgICAvLyBFeGVjdXRlIFR3by1TdGVwIFwiU2VsbCBDcnlwdG8xICYgUmVhY3F1aXJlIENyeXB0bzIgdmlhIFN0YWJsZWNvaW5cIiBmb3IgVGFyZ2V0Um93Tl9taW51c18xXG4gICAgICAgICAgICBjb25zdCBhbW91bnRDcnlwdG8xVG9TZWxsID0gaW5mZXJpb3JSb3cuY3J5cHRvMUFtb3VudEhlbGQ7XG5cbiAgICAgICAgICAgIC8vIFN0ZXAgQTogU2VsbCBDcnlwdG8xIGZvciBQcmVmZXJyZWRTdGFibGVjb2luXG4gICAgICAgICAgICBjb25zdCBjcnlwdG8xU3RhYmxlY29pblByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzEgfHwgJ0JUQycpIC8gZ2V0VVNEUHJpY2UoY29uZmlnLnByZWZlcnJlZFN0YWJsZWNvaW4gfHwgJ1VTRFQnKTtcbiAgICAgICAgICAgIGNvbnN0IHN0YWJsZWNvaW5Gcm9tQzFTZWxsID0gYW1vdW50Q3J5cHRvMVRvU2VsbCAqIGNyeXB0bzFTdGFibGVjb2luUHJpY2U7XG5cbiAgICAgICAgICAgIC8vIFN0ZXAgQjogQnV5IENyeXB0bzIgd2l0aCBTdGFibGVjb2luXG4gICAgICAgICAgICBjb25zdCBjcnlwdG8yU3RhYmxlY29pblByaWNlID0gZ2V0VVNEUHJpY2UoY29uZmlnLmNyeXB0bzIgfHwgJ1VTRFQnKSAvIGdldFVTRFByaWNlKGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICdVU0RUJyk7XG4gICAgICAgICAgICBjb25zdCBjcnlwdG8yUmVhY3F1aXJlZCA9IHN0YWJsZWNvaW5Gcm9tQzFTZWxsIC8gY3J5cHRvMlN0YWJsZWNvaW5QcmljZTtcblxuICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIHJlYWxpemVkIHByb2ZpdCBpbiBDcnlwdG8yXG4gICAgICAgICAgICBjb25zdCByZWFsaXplZFByb2ZpdEluQ3J5cHRvMiA9IGNyeXB0bzJSZWFjcXVpcmVkIC0gaW5mZXJpb3JSb3cub3JpZ2luYWxDb3N0Q3J5cHRvMjtcblxuICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIENyeXB0bzEgcHJvZml0L2xvc3MgYmFzZWQgb24gaW5jb21lIHNwbGl0IHBlcmNlbnRhZ2VcbiAgICAgICAgICAgIGNvbnN0IHJlYWxpemVkUHJvZml0Q3J5cHRvMSA9IGNyeXB0bzFTdGFibGVjb2luUHJpY2UgPiAwID8gKHJlYWxpemVkUHJvZml0SW5DcnlwdG8yICogY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQgLyAxMDApIC8gY3J5cHRvMVN0YWJsZWNvaW5QcmljZSA6IDA7XG5cbiAgICAgICAgICAgIC8vIFVwZGF0ZSBSb3cgTi0xOiBGdWxsIOKGkiBGcmVlLCBMZXZlbCBVTkNIQU5HRUQsIFZhcnMgY2xlYXJlZFxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEluZmVyaW9yUm93OiBUYXJnZXRQcmljZVJvdyA9IHtcbiAgICAgICAgICAgICAgLi4uaW5mZXJpb3JSb3csXG4gICAgICAgICAgICAgIHN0YXR1czogXCJGcmVlXCIsIC8vIEZ1bGwg4oaSIEZyZWVcbiAgICAgICAgICAgICAgLy8gb3JkZXJMZXZlbDogUkVNQUlOUyBVTkNIQU5HRUQgcGVyIHNwZWNpZmljYXRpb25cbiAgICAgICAgICAgICAgY3J5cHRvMUFtb3VudEhlbGQ6IHVuZGVmaW5lZCwgLy8gQ2xlYXIgaGVsZCBhbW91bnRcbiAgICAgICAgICAgICAgb3JpZ2luYWxDb3N0Q3J5cHRvMjogdW5kZWZpbmVkLCAvLyBDbGVhciBvcmlnaW5hbCBjb3N0XG4gICAgICAgICAgICAgIHZhbHVlTGV2ZWw6IGNvbmZpZy5iYXNlQmlkICogTWF0aC5wb3coY29uZmlnLm11bHRpcGxpZXIsIGluZmVyaW9yUm93Lm9yZGVyTGV2ZWwpLCAvLyBSZWNhbGN1bGF0ZSB2YWx1ZSBiYXNlZCBvbiBjdXJyZW50IGxldmVsXG4gICAgICAgICAgICAgIGNyeXB0bzFWYXI6IDAsIC8vIENsZWFyIHRvIDAuMDAgKEZyZWUgc3RhdHVzKVxuICAgICAgICAgICAgICBjcnlwdG8yVmFyOiAwLCAvLyBDbGVhciB0byAwLjAwIChGcmVlIHN0YXR1cylcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1VQREFURV9UQVJHRVRfUFJJQ0VfUk9XJywgcGF5bG9hZDogdXBkYXRlZEluZmVyaW9yUm93IH0pO1xuICAgICAgICAgICAgZGlzcGF0Y2goeyB0eXBlOiAnVVBEQVRFX0JBTEFOQ0VTJywgcGF5bG9hZDogeyBjcnlwdG8xOiBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLSBhbW91bnRDcnlwdG8xVG9TZWxsLCBjcnlwdG8yOiBjdXJyZW50Q3J5cHRvMkJhbGFuY2UgKyBjcnlwdG8yUmVhY3F1aXJlZCB9IH0pO1xuXG4gICAgICAgICAgICAvLyBBZGQgaGlzdG9yeSBlbnRyaWVzIGZvciBib3RoIHN0ZXBzIG9mIHRoZSBOLTEgc3RhYmxlY29pbiBzd2FwXG4gICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgIHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWScsXG4gICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8xfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzEsXG4gICAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIlNFTExcIixcbiAgICAgICAgICAgICAgICBhbW91bnRDcnlwdG8xOiBhbW91bnRDcnlwdG8xVG9TZWxsLFxuICAgICAgICAgICAgICAgIGF2Z1ByaWNlOiBjcnlwdG8xU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbkZyb21DMVNlbGwsXG4gICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8xU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8xIHx8ICcnLFxuICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICcnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICAgIHR5cGU6ICdBRERfT1JERVJfSElTVE9SWV9FTlRSWScsXG4gICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICBpZDogdXVpZHY0KCksXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgICAgICAgIHBhaXI6IGAke2NvbmZpZy5jcnlwdG8yfS8ke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufWAsXG4gICAgICAgICAgICAgICAgY3J5cHRvMTogY29uZmlnLmNyeXB0bzIsXG4gICAgICAgICAgICAgICAgb3JkZXJUeXBlOiBcIkJVWVwiLFxuICAgICAgICAgICAgICAgIGFtb3VudENyeXB0bzE6IGNyeXB0bzJSZWFjcXVpcmVkLFxuICAgICAgICAgICAgICAgIGF2Z1ByaWNlOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIHZhbHVlQ3J5cHRvMjogc3RhYmxlY29pbkZyb21DMVNlbGwsXG4gICAgICAgICAgICAgICAgcHJpY2UxOiBjcnlwdG8yU3RhYmxlY29pblByaWNlLFxuICAgICAgICAgICAgICAgIGNyeXB0bzFTeW1ib2w6IGNvbmZpZy5jcnlwdG8yIHx8ICcnLFxuICAgICAgICAgICAgICAgIGNyeXB0bzJTeW1ib2w6IGNvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2luIHx8ICcnLFxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzI6IHJlYWxpemVkUHJvZml0SW5DcnlwdG8yLFxuICAgICAgICAgICAgICAgIHJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzE6IHJlYWxpemVkUHJvZml0Q3J5cHRvMVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBTVEFCTEVDT0lOIFNFTEw6IENvdW50ZXIgJHtjdXJyZW50Q291bnRlciAtIDF9IHwgU3RlcCBBOiBTb2xkICR7YW1vdW50Q3J5cHRvMVRvU2VsbC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSDihpIgJHtzdGFibGVjb2luRnJvbUMxU2VsbC50b0ZpeGVkKDIpfSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufSB8IFN0ZXAgQjogQm91Z2h0ICR7Y3J5cHRvMlJlYWNxdWlyZWQudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn0gfCBQcm9maXQ6ICR7cmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn0gfCBMZXZlbDogJHtpbmZlcmlvclJvdy5vcmRlckxldmVsfSAodW5jaGFuZ2VkKWApO1xuICAgICAgICAgICAgdG9hc3QoeyB0aXRsZTogXCJTRUxMIEV4ZWN1dGVkIChTdGFibGVjb2luKVwiLCBkZXNjcmlwdGlvbjogYENvdW50ZXIgJHtjdXJyZW50Q291bnRlciAtIDF9OiBQcm9maXQgJHtyZWFsaXplZFByb2ZpdEluQ3J5cHRvMi50b0ZpeGVkKDIpfSAke2NvbmZpZy5jcnlwdG8yfSB2aWEgJHtjb25maWcucHJlZmVycmVkU3RhYmxlY29pbn1gLCBkdXJhdGlvbjogMjAwMCB9KTtcbiAgICAgICAgICAgIHBsYXlTb3VuZCgnc291bmRPcmRlckV4ZWN1dGlvbicpO1xuXG4gICAgICAgICAgICAvLyBTZW5kIFRlbGVncmFtIG5vdGlmaWNhdGlvbiBmb3IgU3RhYmxlY29pbiBTRUxMXG4gICAgICAgICAgICBjb25zdCBwcm9maXRFbW9qaSA9IHJlYWxpemVkUHJvZml0SW5DcnlwdG8yID4gMCA/ICfwn5OIJyA6IHJlYWxpemVkUHJvZml0SW5DcnlwdG8yIDwgMCA/ICfwn5OJJyA6ICfinpYnO1xuICAgICAgICAgICAgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uKFxuICAgICAgICAgICAgICBg8J+UtCA8Yj5TRUxMIEVYRUNVVEVEIChTdGFibGVjb2luIFN3YXApPC9iPlxcbmAgK1xuICAgICAgICAgICAgICBg8J+TiiBDb3VudGVyOiAke2N1cnJlbnRDb3VudGVyIC0gMX1cXG5gICtcbiAgICAgICAgICAgICAgYPCflIQgU3RlcCBBOiBTb2xkICR7YW1vdW50Q3J5cHRvMVRvU2VsbC50b0ZpeGVkKDYpfSAke2NvbmZpZy5jcnlwdG8xfSDihpIgJHtzdGFibGVjb2luRnJvbUMxU2VsbC50b0ZpeGVkKDIpfSAke2NvbmZpZy5wcmVmZXJyZWRTdGFibGVjb2lufVxcbmAgK1xuICAgICAgICAgICAgICBg8J+UhCBTdGVwIEI6IEJvdWdodCAke2NyeXB0bzJSZWFjcXVpcmVkLnRvRml4ZWQoMil9ICR7Y29uZmlnLmNyeXB0bzJ9XFxuYCArXG4gICAgICAgICAgICAgIGAke3Byb2ZpdEVtb2ppfSBQcm9maXQ6ICR7cmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIudG9GaXhlZCgyKX0gJHtjb25maWcuY3J5cHRvMn1cXG5gICtcbiAgICAgICAgICAgICAgYPCfk4ogTGV2ZWw6ICR7aW5mZXJpb3JSb3cub3JkZXJMZXZlbH0gKHVuY2hhbmdlZClcXG5gICtcbiAgICAgICAgICAgICAgYPCfk4ggTW9kZTogU3RhYmxlY29pbiBTd2FwYFxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgYWN0aW9uc1Rha2VuKys7XG5cbiAgICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBiYWxhbmNlIGZvciBzdWJzZXF1ZW50IGNoZWNrcyBpbiB0aGlzIGN5Y2xlXG4gICAgICAgICAgICBjdXJyZW50Q3J5cHRvMUJhbGFuY2UgLT0gYW1vdW50Q3J5cHRvMVRvU2VsbDtcbiAgICAgICAgICAgIGN1cnJlbnRDcnlwdG8yQmFsYW5jZSArPSBjcnlwdG8yUmVhY3F1aXJlZDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoYWN0aW9uc1Rha2VuID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYPCfjq8gQ1lDTEUgQ09NUExFVEU6ICR7YWN0aW9uc1Rha2VufSBhY3Rpb25zIHRha2VuIGF0IHByaWNlICQke2N1cnJlbnRNYXJrZXRQcmljZS50b0ZpeGVkKDIpfWApO1xuICAgIH1cblxuICB9LCBbc3RhdGUuYm90U3lzdGVtU3RhdHVzLCBzdGF0ZS5jdXJyZW50TWFya2V0UHJpY2UsIHN0YXRlLnRhcmdldFByaWNlUm93cywgc3RhdGUuY29uZmlnLCBzdGF0ZS5jcnlwdG8xQmFsYW5jZSwgc3RhdGUuY3J5cHRvMkJhbGFuY2UsIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLCBkaXNwYXRjaCwgdG9hc3QsIHBsYXlTb3VuZCwgc2VuZFRlbGVncmFtTm90aWZpY2F0aW9uXSk7XG5cblxuICBjb25zdCBnZXREaXNwbGF5T3JkZXJzID0gdXNlQ2FsbGJhY2soKCk6IERpc3BsYXlPcmRlclJvd1tdID0+IHtcbiAgICBpZiAoIXN0YXRlLnRhcmdldFByaWNlUm93cyB8fCAhQXJyYXkuaXNBcnJheShzdGF0ZS50YXJnZXRQcmljZVJvd3MpKSB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuXG4gICAgcmV0dXJuIHN0YXRlLnRhcmdldFByaWNlUm93cy5tYXAoKHJvdzogVGFyZ2V0UHJpY2VSb3cpID0+IHtcbiAgICAgIGNvbnN0IGN1cnJlbnRQcmljZSA9IHN0YXRlLmN1cnJlbnRNYXJrZXRQcmljZSB8fCAwO1xuICAgICAgY29uc3QgdGFyZ2V0UHJpY2UgPSByb3cudGFyZ2V0UHJpY2UgfHwgMDtcbiAgICAgIGNvbnN0IHBlcmNlbnRGcm9tQWN0dWFsUHJpY2UgPSBjdXJyZW50UHJpY2UgJiYgdGFyZ2V0UHJpY2VcbiAgICAgICAgPyAoKGN1cnJlbnRQcmljZSAvIHRhcmdldFByaWNlKSAtIDEpICogMTAwXG4gICAgICAgIDogMDtcblxuICAgICAgbGV0IGluY29tZUNyeXB0bzE6IG51bWJlciB8IHVuZGVmaW5lZDtcbiAgICAgIGxldCBpbmNvbWVDcnlwdG8yOiBudW1iZXIgfCB1bmRlZmluZWQ7XG5cbiAgICAgIGlmIChyb3cuc3RhdHVzID09PSBcIkZ1bGxcIiAmJiByb3cuY3J5cHRvMUFtb3VudEhlbGQgJiYgcm93Lm9yaWdpbmFsQ29zdENyeXB0bzIpIHtcbiAgICAgICAgY29uc3QgdG90YWxVbnJlYWxpemVkUHJvZml0SW5DcnlwdG8yID0gKGN1cnJlbnRQcmljZSAqIHJvdy5jcnlwdG8xQW1vdW50SGVsZCkgLSByb3cub3JpZ2luYWxDb3N0Q3J5cHRvMjtcbiAgICAgICAgaW5jb21lQ3J5cHRvMiA9ICh0b3RhbFVucmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgKiBzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCkgLyAxMDA7XG4gICAgICAgIGlmIChjdXJyZW50UHJpY2UgPiAwKSB7XG4gICAgICAgICAgaW5jb21lQ3J5cHRvMSA9ICh0b3RhbFVucmVhbGl6ZWRQcm9maXRJbkNyeXB0bzIgKiBzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudCAvIDEwMCkgLyBjdXJyZW50UHJpY2U7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4ucm93LFxuICAgICAgICBjdXJyZW50UHJpY2UsXG4gICAgICAgIHByaWNlRGlmZmVyZW5jZTogdGFyZ2V0UHJpY2UgLSBjdXJyZW50UHJpY2UsXG4gICAgICAgIHByaWNlRGlmZmVyZW5jZVBlcmNlbnQ6IGN1cnJlbnRQcmljZSA+IDAgPyAoKHRhcmdldFByaWNlIC0gY3VycmVudFByaWNlKSAvIGN1cnJlbnRQcmljZSkgKiAxMDAgOiAwLFxuICAgICAgICBwb3RlbnRpYWxQcm9maXRDcnlwdG8xOiAoc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQgLyAxMDApICogcm93LnZhbHVlTGV2ZWwgLyAodGFyZ2V0UHJpY2UgfHwgMSksXG4gICAgICAgIHBvdGVudGlhbFByb2ZpdENyeXB0bzI6IChzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8yUGVyY2VudCAvIDEwMCkgKiByb3cudmFsdWVMZXZlbCxcbiAgICAgICAgcGVyY2VudEZyb21BY3R1YWxQcmljZSxcbiAgICAgICAgaW5jb21lQ3J5cHRvMSxcbiAgICAgICAgaW5jb21lQ3J5cHRvMixcbiAgICAgIH07XG4gICAgfSkuc29ydCgoYTogRGlzcGxheU9yZGVyUm93LCBiOiBEaXNwbGF5T3JkZXJSb3cpID0+IGIudGFyZ2V0UHJpY2UgLSBhLnRhcmdldFByaWNlKTtcbiAgfSwgW3N0YXRlLnRhcmdldFByaWNlUm93cywgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLCBzdGF0ZS5jb25maWcuaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudCwgc3RhdGUuY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQsIHN0YXRlLmNvbmZpZy5iYXNlQmlkLCBzdGF0ZS5jb25maWcubXVsdGlwbGllcl0pO1xuXG5cbiAgLy8gQmFja2VuZCBJbnRlZ3JhdGlvbiBGdW5jdGlvbnNcbiAgY29uc3Qgc2F2ZUNvbmZpZ1RvQmFja2VuZCA9IHVzZUNhbGxiYWNrKGFzeW5jIChjb25maWc6IFRyYWRpbmdDb25maWcpOiBQcm9taXNlPHN0cmluZyB8IG51bGw+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY29uZmlnRGF0YSA9IHtcbiAgICAgICAgbmFtZTogYCR7Y29uZmlnLmNyeXB0bzF9LyR7Y29uZmlnLmNyeXB0bzJ9ICR7Y29uZmlnLnRyYWRpbmdNb2RlfWAsXG4gICAgICAgIHRyYWRpbmdNb2RlOiBjb25maWcudHJhZGluZ01vZGUsXG4gICAgICAgIGNyeXB0bzE6IGNvbmZpZy5jcnlwdG8xLFxuICAgICAgICBjcnlwdG8yOiBjb25maWcuY3J5cHRvMixcbiAgICAgICAgYmFzZUJpZDogY29uZmlnLmJhc2VCaWQsXG4gICAgICAgIG11bHRpcGxpZXI6IGNvbmZpZy5tdWx0aXBsaWVyLFxuICAgICAgICBudW1EaWdpdHM6IGNvbmZpZy5udW1EaWdpdHMsXG4gICAgICAgIHNsaXBwYWdlUGVyY2VudDogY29uZmlnLnNsaXBwYWdlUGVyY2VudCxcbiAgICAgICAgaW5jb21lU3BsaXRDcnlwdG8xUGVyY2VudDogY29uZmlnLmluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQsXG4gICAgICAgIGluY29tZVNwbGl0Q3J5cHRvMlBlcmNlbnQ6IGNvbmZpZy5pbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50LFxuICAgICAgICBwcmVmZXJyZWRTdGFibGVjb2luOiBjb25maWcucHJlZmVycmVkU3RhYmxlY29pbixcbiAgICAgICAgdGFyZ2V0UHJpY2VzOiBzdGF0ZS50YXJnZXRQcmljZVJvd3MubWFwKHJvdyA9PiByb3cudGFyZ2V0UHJpY2UpXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRyYWRpbmdBcGkuc2F2ZUNvbmZpZyhjb25maWdEYXRhKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQ29uZmlnIHNhdmVkIHRvIGJhY2tlbmQ6JywgcmVzcG9uc2UpO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLmNvbmZpZz8uaWQgfHwgbnVsbDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBzYXZlIGNvbmZpZyB0byBiYWNrZW5kOicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiQmFja2VuZCBFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gc2F2ZSBjb25maWd1cmF0aW9uIHRvIGJhY2tlbmRcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICBkdXJhdGlvbjogMzAwMFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH0sIFtzdGF0ZS50YXJnZXRQcmljZVJvd3MsIHRvYXN0XSk7XG5cbiAgY29uc3Qgc3RhcnRCYWNrZW5kQm90ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbmZpZ0lkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0cmFkaW5nQXBpLnN0YXJ0Qm90KGNvbmZpZ0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQm90IHN0YXJ0ZWQgb24gYmFja2VuZDonLCByZXNwb25zZSk7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkJvdCBTdGFydGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlRyYWRpbmcgYm90IHN0YXJ0ZWQgc3VjY2Vzc2Z1bGx5IG9uIGJhY2tlbmRcIixcbiAgICAgICAgZHVyYXRpb246IDMwMDBcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc3RhcnQgYm90IG9uIGJhY2tlbmQ6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJCYWNrZW5kIEVycm9yXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBzdGFydCBib3Qgb24gYmFja2VuZFwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICB9KTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0sIFt0b2FzdF0pO1xuXG4gIGNvbnN0IHN0b3BCYWNrZW5kQm90ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNvbmZpZ0lkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0cmFkaW5nQXBpLnN0b3BCb3QoY29uZmlnSWQpO1xuICAgICAgY29uc29sZS5sb2coJ+KchSBCb3Qgc3RvcHBlZCBvbiBiYWNrZW5kOicsIHJlc3BvbnNlKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiQm90IFN0b3BwZWRcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiVHJhZGluZyBib3Qgc3RvcHBlZCBzdWNjZXNzZnVsbHkgb24gYmFja2VuZFwiLFxuICAgICAgICBkdXJhdGlvbjogMzAwMFxuICAgICAgfSk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhaWxlZCB0byBzdG9wIGJvdCBvbiBiYWNrZW5kOicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiQmFja2VuZCBFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gc3RvcCBib3Qgb24gYmFja2VuZFwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICB9KTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0sIFt0b2FzdF0pO1xuXG4gIGNvbnN0IGNoZWNrQmFja2VuZFN0YXR1cyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBjb25zdCBhcGlVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMO1xuICAgIGlmICghYXBpVXJsKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjogTkVYVF9QVUJMSUNfQVBJX1VSTCBpcyBub3QgZGVmaW5lZC4gQmFja2VuZCBjb25uZWN0aXZpdHkgY2hlY2sgY2Fubm90IGJlIHBlcmZvcm1lZC4nKTtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9CQUNLRU5EX1NUQVRVUycsIHBheWxvYWQ6ICdvZmZsaW5lJyB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGhlYWx0aFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YXBpVXJsfS9oZWFsdGgvYCk7XG4gICAgICBpZiAoIWhlYWx0aFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIExvZyBtb3JlIGRldGFpbHMgaWYgdGhlIHJlc3BvbnNlIHdhcyByZWNlaXZlZCBidXQgbm90IE9LXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEJhY2tlbmQgaGVhbHRoIGNoZWNrIGZhaWxlZCB3aXRoIHN0YXR1czogJHtoZWFsdGhSZXNwb25zZS5zdGF0dXN9ICR7aGVhbHRoUmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VUZXh0ID0gYXdhaXQgaGVhbHRoUmVzcG9uc2UudGV4dCgpLmNhdGNoKCgpID0+ICdDb3VsZCBub3QgcmVhZCByZXNwb25zZSB0ZXh0LicpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdCYWNrZW5kIGhlYWx0aCBjaGVjayByZXNwb25zZSBib2R5OicsIHJlc3BvbnNlVGV4dCk7XG4gICAgICB9XG4gICAgICBkaXNwYXRjaCh7IHR5cGU6ICdTRVRfQkFDS0VORF9TVEFUVVMnLCBwYXlsb2FkOiBoZWFsdGhSZXNwb25zZS5vayA/ICdvbmxpbmUnIDogJ29mZmxpbmUnIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9CQUNLRU5EX1NUQVRVUycsIHBheWxvYWQ6ICdvZmZsaW5lJyB9KTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0JhY2tlbmQgY29ubmVjdGl2aXR5IGNoZWNrIGZhaWxlZC4gRXJyb3IgZGV0YWlsczonLCBlcnJvcik7XG4gICAgICBpZiAoZXJyb3IuY2F1c2UpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmV0Y2ggZXJyb3IgY2F1c2U6JywgZXJyb3IuY2F1c2UpO1xuICAgICAgfVxuICAgICAgLy8gSXQncyBhbHNvIHVzZWZ1bCB0byBsb2cgdGhlIGFwaVVybCB0byBlbnN1cmUgaXQncyB3aGF0IHdlIGV4cGVjdFxuICAgICAgY29uc29sZS5lcnJvcignQXR0ZW1wdGVkIHRvIGZldGNoIEFQSSBVUkw6JywgYCR7YXBpVXJsfS9oZWFsdGgvYCk7XG4gICAgfVxuICB9LCBbZGlzcGF0Y2hdKTtcblxuICAvLyBJbml0aWFsaXplIGJhY2tlbmQgc3RhdHVzIGNoZWNrIChvbmUtdGltZSBvbmx5KVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXRpYWwgY2hlY2sgZm9yIGJhY2tlbmQgc3RhdHVzIG9ubHlcbiAgICBjaGVja0JhY2tlbmRTdGF0dXMoKTtcbiAgfSwgW2NoZWNrQmFja2VuZFN0YXR1c10pO1xuXG4gIC8vIFNhdmUgc3RhdGUgdG8gbG9jYWxTdG9yYWdlIHdoZW5ldmVyIGl0IGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzYXZlU3RhdGVUb0xvY2FsU3RvcmFnZShzdGF0ZSk7XG4gIH0sIFtzdGF0ZV0pO1xuXG5cbiAgLy8gRWZmZWN0IHRvIGhhbmRsZSBib3Qgd2FybS11cCBwZXJpb2QgKGltbWVkaWF0ZSBleGVjdXRpb24pXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1dhcm1pbmdVcCcpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiQm90IGlzIFdhcm1pbmcgVXAuLi4gSW1tZWRpYXRlIGV4ZWN1dGlvbiBlbmFibGVkLlwiKTtcbiAgICAgIC8vIEltbWVkaWF0ZSB0cmFuc2l0aW9uIHRvIFJ1bm5pbmcgc3RhdGUgLSBubyBkZWxheXNcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NZU1RFTV9DT01QTEVURV9XQVJNVVAnIH0pO1xuICAgICAgY29uc29sZS5sb2coXCJCb3QgaXMgbm93IFJ1bm5pbmcgaW1tZWRpYXRlbHkuXCIpO1xuICAgIH1cbiAgfSwgW3N0YXRlLmJvdFN5c3RlbVN0YXR1cywgZGlzcGF0Y2hdKTtcblxuICAvLyBBdXRvLWNyZWF0ZSBzZXNzaW9uIHdoZW4gYm90IHN0YXJ0cyBpZiBub25lIGV4aXN0c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNlc3Npb25NYW5hZ2VyID0gU2Vzc2lvbk1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcblxuICAgIC8vIENoZWNrIGlmIHdlIG5lZWQgdG8gY3JlYXRlIGEgc2Vzc2lvbiB3aGVuIGJvdCBzdGFydHNcbiAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnV2FybWluZ1VwJyAmJiAhc2Vzc2lvbk1hbmFnZXIuZ2V0Q3VycmVudFNlc3Npb25JZCgpKSB7XG4gICAgICAvLyBPbmx5IGNyZWF0ZSBpZiB3ZSBoYXZlIHZhbGlkIGNyeXB0byBjb25maWd1cmF0aW9uXG4gICAgICBpZiAoc3RhdGUuY29uZmlnLmNyeXB0bzEgJiYgc3RhdGUuY29uZmlnLmNyeXB0bzIpIHtcbiAgICAgICAgc2Vzc2lvbk1hbmFnZXIuY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZShzdGF0ZS5jb25maWcpXG4gICAgICAgICAgLnRoZW4oKHNlc3Npb25JZCkgPT4ge1xuICAgICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2V0Q3VycmVudFNlc3Npb24oc2Vzc2lvbklkKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgQXV0by1jcmVhdGVkIHNlc3Npb246Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgICB9KVxuICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gYXV0by1jcmVhdGUgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGUuYm90U3lzdGVtU3RhdHVzLCBzdGF0ZS5jb25maWcuY3J5cHRvMSwgc3RhdGUuY29uZmlnLmNyeXB0bzJdKTtcblxuICAvLyBJbml0aWFsaXplIG5ldHdvcmsgbW9uaXRvcmluZyBhbmQgYXV0by1zYXZlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbmV0d29ya01vbml0b3IgPSBOZXR3b3JrTW9uaXRvci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IGF1dG9TYXZlTWFuYWdlciA9IEF1dG9TYXZlTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuICAgIGNvbnN0IG1lbW9yeU1vbml0b3IgPSBNZW1vcnlNb25pdG9yLmdldEluc3RhbmNlKCk7XG4gICAgY29uc3Qgc2Vzc2lvbk1hbmFnZXIgPSBTZXNzaW9uTWFuYWdlci5nZXRJbnN0YW5jZSgpO1xuXG4gICAgLy8gU2V0IHVwIG5ldHdvcmsgc3RhdHVzIGxpc3RlbmVyXG4gICAgY29uc3QgdW5zdWJzY3JpYmVOZXR3b3JrID0gbmV0d29ya01vbml0b3IuYWRkTGlzdGVuZXIoKGlzT25saW5lLCBpc0luaXRpYWwpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn4yQIE5ldHdvcmsgc3RhdHVzIGNoYW5nZWQ6ICR7aXNPbmxpbmUgPyAnT25saW5lJyA6ICdPZmZsaW5lJ31gKTtcblxuICAgICAgLy8gT25seSBzaG93IHRvYXN0IG5vdGlmaWNhdGlvbnMgZm9yIGFjdHVhbCBzdGF0ZSBjaGFuZ2VzLCBub3QgaW5pdGlhbCBzdGF0ZVxuICAgICAgaWYgKCFpc0luaXRpYWwpIHtcbiAgICAgICAgaWYgKCFpc09ubGluZSkge1xuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiBcIk5ldHdvcmsgRGlzY29ubmVjdGVkXCIsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJZb3UgYXJlIGN1cnJlbnRseSBvZmZsaW5lLiBEYXRhIHdpbGwgYmUgc2F2ZWQgbG9jYWxseS5cIixcbiAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgICAgICAgIGR1cmF0aW9uOiA1MDAwXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgICAgdGl0bGU6IFwiTmV0d29yayBSZWNvbm5lY3RlZFwiLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiQ29ubmVjdGlvbiByZXN0b3JlZC4gQXV0by1zYXZpbmcgZW5hYmxlZC5cIixcbiAgICAgICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFNldCB1cCBtZW1vcnkgbW9uaXRvcmluZ1xuICAgIGNvbnN0IHVuc3Vic2NyaWJlTWVtb3J5ID0gbWVtb3J5TW9uaXRvci5hZGRMaXN0ZW5lcigobWVtb3J5KSA9PiB7XG4gICAgICBjb25zdCB1c2VkTUIgPSBtZW1vcnkudXNlZEpTSGVhcFNpemUgLyAxMDI0IC8gMTAyNDtcbiAgICAgIGlmICh1c2VkTUIgPiAxNTApIHsgLy8gMTUwTUIgdGhyZXNob2xkXG4gICAgICAgIGNvbnNvbGUud2Fybihg8J+noCBIaWdoIG1lbW9yeSB1c2FnZTogJHt1c2VkTUIudG9GaXhlZCgyKX1NQmApO1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IFwiSGlnaCBNZW1vcnkgVXNhZ2VcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYE1lbW9yeSB1c2FnZSBpcyBoaWdoICgke3VzZWRNQi50b0ZpeGVkKDApfU1CKS4gQ29uc2lkZXIgcmVmcmVzaGluZyB0aGUgcGFnZS5gLFxuICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgICAgICBkdXJhdGlvbjogNTAwMFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFNldCB1cCBhdXRvLXNhdmVcbiAgICBjb25zdCBzYXZlRnVuY3Rpb24gPSAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBTYXZlIHRvIHNlc3Npb24gbWFuYWdlciBpZiB3ZSBoYXZlIGEgY3VycmVudCBzZXNzaW9uXG4gICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uTWFuYWdlci5nZXRDdXJyZW50U2Vzc2lvbklkKCk7XG4gICAgICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICAgICAgc2Vzc2lvbk1hbmFnZXIuc2F2ZVNlc3Npb24oXG4gICAgICAgICAgICBjdXJyZW50U2Vzc2lvbklkLFxuICAgICAgICAgICAgc3RhdGUuY29uZmlnLFxuICAgICAgICAgICAgc3RhdGUudGFyZ2V0UHJpY2VSb3dzLFxuICAgICAgICAgICAgc3RhdGUub3JkZXJIaXN0b3J5LFxuICAgICAgICAgICAgc3RhdGUuY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgc3RhdGUuY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICBzdGF0ZS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgIHN0YXRlLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZydcbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQWxzbyBzYXZlIHRvIGxvY2FsU3RvcmFnZSBhcyBiYWNrdXBcbiAgICAgICAgc2F2ZVN0YXRlVG9Mb2NhbFN0b3JhZ2Uoc3RhdGUpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0by1zYXZlIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGF1dG9TYXZlTWFuYWdlci5lbmFibGUoc2F2ZUZ1bmN0aW9uLCAzMDAwMCk7IC8vIEF1dG8tc2F2ZSBldmVyeSAzMCBzZWNvbmRzXG5cbiAgICAvLyBBZGQgYmVmb3JldW5sb2FkIGxpc3RlbmVyIHRvIHNhdmUgc2Vzc2lvbiBvbiBicm93c2VyIGNsb3NlL3JlZnJlc2hcbiAgICBjb25zdCBoYW5kbGVCZWZvcmVVbmxvYWQgPSAoZXZlbnQ6IEJlZm9yZVVubG9hZEV2ZW50KSA9PiB7XG4gICAgICAvLyBTYXZlIGltbWVkaWF0ZWx5IGJlZm9yZSB1bmxvYWRcbiAgICAgIHNhdmVGdW5jdGlvbigpO1xuXG4gICAgICAvLyBJZiBib3QgaXMgcnVubmluZywgc2hvdyB3YXJuaW5nXG4gICAgICBpZiAoc3RhdGUuYm90U3lzdGVtU3RhdHVzID09PSAnUnVubmluZycpIHtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9ICdUcmFkaW5nIGJvdCBpcyBjdXJyZW50bHkgcnVubmluZy4gQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGxlYXZlPyc7XG4gICAgICAgIGV2ZW50LnJldHVyblZhbHVlID0gbWVzc2FnZTtcbiAgICAgICAgcmV0dXJuIG1lc3NhZ2U7XG4gICAgICB9XG4gICAgfTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpO1xuXG4gICAgLy8gQ2xlYW51cCBmdW5jdGlvblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB1bnN1YnNjcmliZU5ldHdvcmsoKTtcbiAgICAgIHVuc3Vic2NyaWJlTWVtb3J5KCk7XG4gICAgICBhdXRvU2F2ZU1hbmFnZXIuZGlzYWJsZSgpO1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XG4gICAgfTtcbiAgfSwgW3N0YXRlLCB0b2FzdF0pO1xuXG4gIC8vIEZvcmNlIHNhdmUgd2hlbiBib3Qgc3RhdHVzIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBhdXRvU2F2ZU1hbmFnZXIgPSBBdXRvU2F2ZU1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgICBhdXRvU2F2ZU1hbmFnZXIuc2F2ZU5vdygpO1xuICB9LCBbc3RhdGUuYm90U3lzdGVtU3RhdHVzXSk7XG5cblxuICAvLyBDb250ZXh0IHZhbHVlXG4gIGNvbnN0IGNvbnRleHRWYWx1ZTogVHJhZGluZ0NvbnRleHRUeXBlID0ge1xuICAgIC4uLnN0YXRlLFxuICAgIGRpc3BhdGNoLFxuICAgIHNldFRhcmdldFByaWNlcyxcbiAgICBnZXREaXNwbGF5T3JkZXJzLFxuICAgIGNoZWNrQmFja2VuZFN0YXR1cyxcbiAgICBmZXRjaE1hcmtldFByaWNlLFxuICAgIHN0YXJ0QmFja2VuZEJvdCxcbiAgICBzdG9wQmFja2VuZEJvdCxcbiAgICBzYXZlQ29uZmlnVG9CYWNrZW5kLFxuICAgIGJhY2tlbmRTdGF0dXM6IHN0YXRlLmJhY2tlbmRTdGF0dXMgYXMgJ29ubGluZScgfCAnb2ZmbGluZScgfCAndW5rbm93bicsXG4gICAgYm90U3lzdGVtU3RhdHVzOiBzdGF0ZS5ib3RTeXN0ZW1TdGF0dXMsXG4gICAgaXNCb3RBY3RpdmU6IHN0YXRlLmJvdFN5c3RlbVN0YXR1cyA9PT0gJ1J1bm5pbmcnLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPFRyYWRpbmdDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtjb250ZXh0VmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVHJhZGluZ0NvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG4vLyBDdXN0b20gaG9vayB0byB1c2UgdGhlIHRyYWRpbmcgY29udGV4dFxuZXhwb3J0IGNvbnN0IHVzZVRyYWRpbmdDb250ZXh0ID0gKCk6IFRyYWRpbmdDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRyYWRpbmdDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlVHJhZGluZ0NvbnRleHQgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRyYWRpbmdQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VSZWR1Y2VyIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJERUZBVUxUX0FQUF9TRVRUSU5HUyIsIkFWQUlMQUJMRV9TVEFCTEVDT0lOUyIsInY0IiwidXVpZHY0IiwiREVGQVVMVF9RVU9URV9DVVJSRU5DSUVTIiwidXNlVG9hc3QiLCJ0cmFkaW5nQXBpIiwiU2Vzc2lvbk1hbmFnZXIiLCJOZXR3b3JrTW9uaXRvciIsIkF1dG9TYXZlTWFuYWdlciIsIk1lbW9yeU1vbml0b3IiLCJjYWxjdWxhdGVJbml0aWFsTWFya2V0UHJpY2UiLCJjb25maWciLCJjcnlwdG8xIiwiY3J5cHRvMiIsImNhbGN1bGF0ZUZhbGxiYWNrTWFya2V0UHJpY2UiLCJnZXRNYXJrZXRQcmljZUZyb21BUEkiLCJzeW1ib2wiLCJ0b1VwcGVyQ2FzZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImRhdGEiLCJqc29uIiwicHJpY2UiLCJwYXJzZUZsb2F0IiwiY29uc29sZSIsImxvZyIsImJpbmFuY2VFcnJvciIsIndhcm4iLCJjcnlwdG8xSWQiLCJnZXRDb2luR2Vja29JZCIsImNyeXB0bzJJZCIsImdlY2tvRXJyb3IiLCJtb2NrUHJpY2UiLCJlcnJvciIsIm1hcHBpbmciLCJnZXRTdGFibGVjb2luRXhjaGFuZ2VSYXRlIiwiY3J5cHRvIiwic3RhYmxlY29pbiIsImNyeXB0b0lkIiwic3RhYmxlY29pbklkIiwicmF0ZSIsImNyeXB0b1VTRFByaWNlIiwiZ2V0VVNEUHJpY2UiLCJzdGFibGVjb2luVVNEUHJpY2UiLCJ1c2RQcmljZXMiLCJjcnlwdG8xVVNEUHJpY2UiLCJjcnlwdG8yVVNEUHJpY2UiLCJiYXNlUHJpY2UiLCJmbHVjdHVhdGlvbiIsIk1hdGgiLCJyYW5kb20iLCJmaW5hbFByaWNlIiwidG9GaXhlZCIsImluaXRpYWxCYXNlQ29uZmlnIiwidHJhZGluZ01vZGUiLCJiYXNlQmlkIiwibXVsdGlwbGllciIsIm51bURpZ2l0cyIsInNsaXBwYWdlUGVyY2VudCIsImluY29tZVNwbGl0Q3J5cHRvMVBlcmNlbnQiLCJpbmNvbWVTcGxpdENyeXB0bzJQZXJjZW50IiwicHJlZmVycmVkU3RhYmxlY29pbiIsImluaXRpYWxUcmFkaW5nU3RhdGUiLCJ0YXJnZXRQcmljZVJvd3MiLCJvcmRlckhpc3RvcnkiLCJhcHBTZXR0aW5ncyIsImN1cnJlbnRNYXJrZXRQcmljZSIsImJvdFN5c3RlbVN0YXR1cyIsImNyeXB0bzFCYWxhbmNlIiwiY3J5cHRvMkJhbGFuY2UiLCJzdGFibGVjb2luQmFsYW5jZSIsImJhY2tlbmRTdGF0dXMiLCJsYXN0QWN0aW9uVGltZXN0YW1wUGVyQ291bnRlciIsIk1hcCIsIlNUT1JBR0VfS0VZIiwic2F2ZVN0YXRlVG9Mb2NhbFN0b3JhZ2UiLCJzdGF0ZSIsInN0YXRlVG9TYXZlIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5IiwibG9hZFN0YXRlRnJvbUxvY2FsU3RvcmFnZSIsInNhdmVkU3RhdGUiLCJnZXRJdGVtIiwicGFyc2VkIiwicGFyc2UiLCJ0cmFkaW5nUmVkdWNlciIsImFjdGlvbiIsInR5cGUiLCJuZXdDb25maWciLCJwYXlsb2FkIiwic29ydCIsImEiLCJiIiwidGFyZ2V0UHJpY2UiLCJtYXAiLCJyb3ciLCJpbmRleCIsImNvdW50ZXIiLCJuZXdSb3dzIiwidXBkYXRlZFJvd3MiLCJpZCIsImZpbHRlcmVkUm93cyIsImZpbHRlciIsImZsdWN0dWF0aW9uRmFjdG9yIiwibmV3UHJpY2UiLCJ1bmRlZmluZWQiLCJjb25maWdGb3JSZXNldCIsImNsZWFyIiwiVHJhZGluZ0NvbnRleHQiLCJUcmFkaW5nUHJvdmlkZXIiLCJjaGlsZHJlbiIsImluaXRpYWxpemVTdGF0ZSIsImRpc3BhdGNoIiwidG9hc3QiLCJhdWRpb1JlZiIsImZldGNoTWFya2V0UHJpY2UiLCJwcmljZUZsdWN0dWF0aW9uSW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJjdXJyZW50IiwiQXVkaW8iLCJwbGF5U291bmQiLCJzb3VuZEtleSIsInNvdW5kQWxlcnRzRW5hYmxlZCIsInNvdW5kUGF0aCIsImFsZXJ0T25PcmRlckV4ZWN1dGlvbiIsInNvdW5kT3JkZXJFeGVjdXRpb24iLCJhbGVydE9uRXJyb3IiLCJzb3VuZEVycm9yIiwic3JjIiwiY3VycmVudFRpbWUiLCJwbGF5IiwidGhlbiIsInNldFRpbWVvdXQiLCJwYXVzZSIsImNhdGNoIiwiZXJyIiwic2VuZFRlbGVncmFtTm90aWZpY2F0aW9uIiwibWVzc2FnZSIsInRlbGVncmFtVG9rZW4iLCJ0ZWxlZ3JhbUNoYXRJZCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiY2hhdF9pZCIsInRleHQiLCJwYXJzZV9tb2RlIiwic3RhdHVzVGV4dCIsInNldFRhcmdldFByaWNlcyIsInByaWNlcyIsIkFycmF5IiwiaXNBcnJheSIsInNvcnRlZFByaWNlcyIsImlzTmFOIiwiZXhpc3RpbmdSb3ciLCJmaW5kIiwiciIsInN0YXR1cyIsIm9yZGVyTGV2ZWwiLCJ2YWx1ZUxldmVsIiwibGVuZ3RoIiwic29ydGVkUm93c0ZvckxvZ2ljIiwiY3VycmVudENyeXB0bzFCYWxhbmNlIiwiY3VycmVudENyeXB0bzJCYWxhbmNlIiwiYWN0aW9uc1Rha2VuIiwidGFyZ2V0c0luUmFuZ2UiLCJkaWZmUGVyY2VudCIsImFicyIsImkiLCJhY3RpdmVSb3ciLCJwcmljZURpZmZQZXJjZW50IiwiY29zdENyeXB0bzIiLCJhbW91bnRDcnlwdG8xQm91Z2h0IiwidXBkYXRlZFJvdyIsInBvdyIsImNyeXB0bzFBbW91bnRIZWxkIiwib3JpZ2luYWxDb3N0Q3J5cHRvMiIsImNyeXB0bzFWYXIiLCJjcnlwdG8yVmFyIiwicGFpciIsIm9yZGVyVHlwZSIsImFtb3VudENyeXB0bzEiLCJhdmdQcmljZSIsInZhbHVlQ3J5cHRvMiIsInByaWNlMSIsImNyeXB0bzFTeW1ib2wiLCJjcnlwdG8yU3ltYm9sIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImR1cmF0aW9uIiwiY3VycmVudENvdW50ZXIiLCJpbmZlcmlvclJvdyIsImFtb3VudENyeXB0bzFUb1NlbGwiLCJjcnlwdG8yUmVjZWl2ZWQiLCJyZWFsaXplZFByb2ZpdCIsInJlYWxpemVkUHJvZml0Q3J5cHRvMSIsInVwZGF0ZWRJbmZlcmlvclJvdyIsInJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIiLCJyZWFsaXplZFByb2ZpdExvc3NDcnlwdG8xIiwicHJvZml0RW1vamkiLCJhbW91bnRDcnlwdG8yVG9Vc2UiLCJjcnlwdG8yU3RhYmxlY29pblByaWNlIiwic3RhYmxlY29pbk9idGFpbmVkIiwiY3J5cHRvMVN0YWJsZWNvaW5QcmljZSIsImNyeXB0bzFCb3VnaHQiLCJuZXdMZXZlbCIsIm5ld1ZhbHVlIiwic3RhYmxlY29pbkZyb21DMVNlbGwiLCJjcnlwdG8yUmVhY3F1aXJlZCIsInJlYWxpemVkUHJvZml0SW5DcnlwdG8yIiwiZ2V0RGlzcGxheU9yZGVycyIsImN1cnJlbnRQcmljZSIsInBlcmNlbnRGcm9tQWN0dWFsUHJpY2UiLCJpbmNvbWVDcnlwdG8xIiwiaW5jb21lQ3J5cHRvMiIsInRvdGFsVW5yZWFsaXplZFByb2ZpdEluQ3J5cHRvMiIsInByaWNlRGlmZmVyZW5jZSIsInByaWNlRGlmZmVyZW5jZVBlcmNlbnQiLCJwb3RlbnRpYWxQcm9maXRDcnlwdG8xIiwicG90ZW50aWFsUHJvZml0Q3J5cHRvMiIsInNhdmVDb25maWdUb0JhY2tlbmQiLCJjb25maWdEYXRhIiwibmFtZSIsInRhcmdldFByaWNlcyIsInNhdmVDb25maWciLCJ2YXJpYW50Iiwic3RhcnRCYWNrZW5kQm90IiwiY29uZmlnSWQiLCJzdGFydEJvdCIsInN0b3BCYWNrZW5kQm90Iiwic3RvcEJvdCIsImNoZWNrQmFja2VuZFN0YXR1cyIsImFwaVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiaGVhbHRoUmVzcG9uc2UiLCJyZXNwb25zZVRleHQiLCJjYXVzZSIsInNlc3Npb25NYW5hZ2VyIiwiZ2V0SW5zdGFuY2UiLCJnZXRDdXJyZW50U2Vzc2lvbklkIiwiY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZSIsInNlc3Npb25JZCIsInNldEN1cnJlbnRTZXNzaW9uIiwibmV0d29ya01vbml0b3IiLCJhdXRvU2F2ZU1hbmFnZXIiLCJtZW1vcnlNb25pdG9yIiwidW5zdWJzY3JpYmVOZXR3b3JrIiwiYWRkTGlzdGVuZXIiLCJpc09ubGluZSIsImlzSW5pdGlhbCIsInVuc3Vic2NyaWJlTWVtb3J5IiwibWVtb3J5IiwidXNlZE1CIiwidXNlZEpTSGVhcFNpemUiLCJzYXZlRnVuY3Rpb24iLCJjdXJyZW50U2Vzc2lvbklkIiwic2F2ZVNlc3Npb24iLCJlbmFibGUiLCJoYW5kbGVCZWZvcmVVbmxvYWQiLCJldmVudCIsInJldHVyblZhbHVlIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsImRpc2FibGUiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwic2F2ZU5vdyIsImNvbnRleHRWYWx1ZSIsImlzQm90QWN0aXZlIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVRyYWRpbmdDb250ZXh0IiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});