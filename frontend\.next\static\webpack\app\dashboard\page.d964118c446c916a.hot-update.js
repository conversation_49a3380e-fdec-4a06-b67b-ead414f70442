"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardOrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/OrdersTable */ \"(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\");\n/* harmony import */ var _components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/DashboardTabs */ \"(app-pages-browser)/./src/components/dashboard/DashboardTabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/BalancesDisplay */ \"(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\");\n/* harmony import */ var _components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/MarketPriceDisplay */ \"(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardOrdersPage() {\n    _s();\n    const { config, saveCurrentSession } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [currentSessionName, setCurrentSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_8__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardOrdersPage.useEffect\": ()=>{\n            const updateSessionName = {\n                \"DashboardOrdersPage.useEffect.updateSessionName\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        const session = sessionManager.loadSession(currentSessionId);\n                        if (session) {\n                            setCurrentSessionName(session.name);\n                            return;\n                        }\n                    }\n                    // No session or session not found, generate default name\n                    if (config.crypto1 && config.crypto2) {\n                        const defaultName = \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot');\n                        setCurrentSessionName(defaultName);\n                    } else {\n                        setCurrentSessionName('Crypto 1/Crypto 2 = 0');\n                    }\n                }\n            }[\"DashboardOrdersPage.useEffect.updateSessionName\"];\n            updateSessionName();\n            // Also listen for storage changes to update when session is cleared\n            const handleStorageChange = {\n                \"DashboardOrdersPage.useEffect.handleStorageChange\": ()=>{\n                    updateSessionName();\n                }\n            }[\"DashboardOrdersPage.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"DashboardOrdersPage.useEffect\": ()=>window.removeEventListener('storage', handleStorageChange)\n            })[\"DashboardOrdersPage.useEffect\"];\n        }\n    }[\"DashboardOrdersPage.useEffect\"], [\n        config.crypto1,\n        config.crypto2,\n        config.tradingMode,\n        sessionManager\n    ]);\n    // Show placeholder text when cryptos are not selected\n    const displayTitle = currentSessionName || (config.crypto1 && config.crypto2 ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode || 'SimpleSpot') : \"Crypto 1/Crypto 2 = 0\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardTabs__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_BalancesDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-2 border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: [\n                                    \"Active Orders (\",\n                                    displayTitle,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Current state of your target price levels. Prices update in real-time.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MarketPriceDisplay__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_OrdersTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOrdersPage, \"s8H83B6nhFivw+hj+iqQivUZg/s=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_5__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = DashboardOrdersPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardOrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});