"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/ui/backend-status.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/backend-status.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackendStatus: () => (/* binding */ BackendStatus),\n/* harmony export */   NetworkStatus: () => (/* binding */ NetworkStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ NetworkStatus,BackendStatus auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction NetworkStatus(param) {\n    let { className = \"\" } = param;\n    _s();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(navigator.onLine);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NetworkStatus.useEffect\": ()=>{\n            const handleOnline = {\n                \"NetworkStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"NetworkStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"NetworkStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"NetworkStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Update time every second\n            const timeInterval = setInterval({\n                \"NetworkStatus.useEffect.timeInterval\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"NetworkStatus.useEffect.timeInterval\"], 1000);\n            return ({\n                \"NetworkStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(timeInterval);\n                }\n            })[\"NetworkStatus.useEffect\"];\n        }\n    }[\"NetworkStatus.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: isOnline ? \"default\" : \"destructive\",\n                className: \"flex items-center gap-1\",\n                children: [\n                    isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 52\n                    }, this),\n                    isOnline ? \"Online\" : \"Offline\"\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: currentTime.toLocaleTimeString()\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(NetworkStatus, \"VjabOel5zz4nMKHNXxYvXtBDtOU=\");\n_c = NetworkStatus;\nfunction BackendStatus(param) {\n    let { className = \"\", showDetails = false } = param;\n    _s1();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkBackendStatus = async ()=>{\n        setIsChecking(true);\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 2000);\n            const response = await fetch('http://localhost:5000/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            setIsOnline(response.status < 500);\n            setLastCheck(new Date());\n        } catch (error) {\n            setIsOnline(false);\n            setLastCheck(new Date());\n        } finally{\n            setIsChecking(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BackendStatus.useEffect\": ()=>{\n            checkBackendStatus();\n            // Check every 30 seconds\n            const interval = setInterval(checkBackendStatus, 30000);\n            return ({\n                \"BackendStatus.useEffect\": ()=>clearInterval(interval)\n            })[\"BackendStatus.useEffect\"];\n        }\n    }[\"BackendStatus.useEffect\"], []);\n    if (!showDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n            variant: isOnline ? \"default\" : \"destructive\",\n            className: \"flex items-center gap-1 \".concat(className),\n            children: [\n                isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 52\n                }, this),\n                isOnline ? \"Backend Online\" : \"Backend Offline\"\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"\".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isOnline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Backend Status: \",\n                                        isOnline ? \"Online\" : \"Offline\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: checkBackendStatus,\n                            disabled: isChecking,\n                            children: [\n                                isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                \"Check\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-2\",\n                    children: [\n                        \"Last checked: \",\n                        lastCheck.toLocaleTimeString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this),\n                !isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-yellow-600 mb-1\",\n                                        children: \"Backend Offline\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-700\",\n                                        children: \"Some features may be limited. Session management will use local storage.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-yellow-600 mt-2\",\n                                        children: [\n                                            \"To start the backend: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-yellow-100 px-1 rounded\",\n                                                children: \"python run.py\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this),\n                isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 p-3 bg-green-500/10 border border-green-500/20 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-700\",\n                                children: \"All features available. Backend connected successfully.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\ui\\\\backend-status.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s1(BackendStatus, \"Hl8rfuJr7E5y7GxmtjkRpiA4cDc=\");\n_c1 = BackendStatus;\nvar _c, _c1;\n$RefreshReg$(_c, \"NetworkStatus\");\n$RefreshReg$(_c1, \"BackendStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2JhY2tlbmQtc3RhdHVzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDTDtBQUNFO0FBQ1M7QUFDcUI7QUFXdkUsU0FBU1ksY0FBYyxLQUFzQztRQUF0QyxFQUFFQyxZQUFZLEVBQUUsRUFBc0IsR0FBdEM7O0lBQzVCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHZCwrQ0FBUUEsQ0FBQ2UsVUFBVUMsTUFBTTtJQUN6RCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDLElBQUltQjtJQUVuRGxCLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1tQjt3REFBZSxJQUFNTixZQUFZOztZQUN2QyxNQUFNTzt5REFBZ0IsSUFBTVAsWUFBWTs7WUFFeENRLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDRSxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXRjtZQUVuQywyQkFBMkI7WUFDM0IsTUFBTUcsZUFBZUM7d0RBQVk7b0JBQy9CUCxlQUFlLElBQUlDO2dCQUNyQjt1REFBRztZQUVIOzJDQUFPO29CQUNMRyxPQUFPSSxtQkFBbUIsQ0FBQyxVQUFVTjtvQkFDckNFLE9BQU9JLG1CQUFtQixDQUFDLFdBQVdMO29CQUN0Q00sY0FBY0g7Z0JBQ2hCOztRQUNGO2tDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ0k7UUFBSWhCLFdBQVcsMkJBQXFDLE9BQVZBOzswQkFDekMsOERBQUNWLHVEQUFLQTtnQkFDSjJCLFNBQVNoQixXQUFXLFlBQVk7Z0JBQ2hDRCxXQUFVOztvQkFFVEMseUJBQVcsOERBQUNQLHNIQUFJQTt3QkFBQ00sV0FBVTs7Ozs7NkNBQWUsOERBQUNMLHNIQUFPQTt3QkFBQ0ssV0FBVTs7Ozs7O29CQUM3REMsV0FBVyxXQUFXOzs7Ozs7OzBCQUV6Qiw4REFBQ2U7Z0JBQUloQixXQUFVOztrQ0FDYiw4REFBQ0Ysc0hBQUtBO3dCQUFDRSxXQUFVOzs7Ozs7a0NBQ2pCLDhEQUFDa0I7a0NBQU1iLFlBQVljLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTdDO0dBdENnQnBCO0tBQUFBO0FBd0NULFNBQVNxQixjQUFjLEtBQTJEO1FBQTNELEVBQUVwQixZQUFZLEVBQUUsRUFBRXFCLGNBQWMsS0FBSyxFQUFzQixHQUEzRDs7SUFDNUIsTUFBTSxDQUFDcEIsVUFBVUMsWUFBWSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNrQyxZQUFZQyxjQUFjLEdBQUduQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNvQyxXQUFXQyxhQUFhLEdBQUdyQywrQ0FBUUEsQ0FBYztJQUV4RCxNQUFNc0MscUJBQXFCO1FBQ3pCSCxjQUFjO1FBQ2QsSUFBSTtZQUNGLE1BQU1JLGFBQWEsSUFBSUM7WUFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUk7WUFFdkQsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtnQkFDckRDLFFBQVE7Z0JBQ1JDLFFBQVFSLFdBQVdRLE1BQU07WUFDM0I7WUFFQUMsYUFBYVA7WUFDYjNCLFlBQVk4QixTQUFTSyxNQUFNLEdBQUc7WUFDOUJaLGFBQWEsSUFBSWxCO1FBQ25CLEVBQUUsT0FBTytCLE9BQU87WUFDZHBDLFlBQVk7WUFDWnVCLGFBQWEsSUFBSWxCO1FBQ25CLFNBQVU7WUFDUmdCLGNBQWM7UUFDaEI7SUFDRjtJQUVBbEMsZ0RBQVNBO21DQUFDO1lBQ1JxQztZQUVBLHlCQUF5QjtZQUN6QixNQUFNYSxXQUFXMUIsWUFBWWEsb0JBQW9CO1lBRWpEOzJDQUFPLElBQU1YLGNBQWN3Qjs7UUFDN0I7a0NBQUcsRUFBRTtJQUVMLElBQUksQ0FBQ2xCLGFBQWE7UUFDaEIscUJBQ0UsOERBQUMvQix1REFBS0E7WUFDSjJCLFNBQVNoQixXQUFXLFlBQVk7WUFDaENELFdBQVcsMkJBQXFDLE9BQVZBOztnQkFFckNDLHlCQUFXLDhEQUFDUCxzSEFBSUE7b0JBQUNNLFdBQVU7Ozs7O3lDQUFlLDhEQUFDTCxzSEFBT0E7b0JBQUNLLFdBQVU7Ozs7OztnQkFDN0RDLFdBQVcsbUJBQW1COzs7Ozs7O0lBR3JDO0lBRUEscUJBQ0UsOERBQUNULHFEQUFJQTtRQUFDUSxXQUFXLEdBQWEsT0FBVkE7a0JBQ2xCLDRFQUFDUCw0REFBV0E7WUFBQ08sV0FBVTs7OEJBQ3JCLDhEQUFDZ0I7b0JBQUloQixXQUFVOztzQ0FDYiw4REFBQ2dCOzRCQUFJaEIsV0FBVTs7Z0NBQ1pDLHlCQUNDLDhEQUFDUCxzSEFBSUE7b0NBQUNNLFdBQVU7Ozs7O3lEQUVoQiw4REFBQ0wsc0hBQU9BO29DQUFDSyxXQUFVOzs7Ozs7OENBRXJCLDhEQUFDa0I7b0NBQUtsQixXQUFVOzt3Q0FBYzt3Q0FDWEMsV0FBVyxXQUFXOzs7Ozs7Ozs7Ozs7O3NDQUkzQyw4REFBQ1YseURBQU1BOzRCQUNMMEIsU0FBUTs0QkFDUnVCLE1BQUs7NEJBQ0xDLFNBQVNmOzRCQUNUZ0IsVUFBVXBCOztnQ0FFVEEsMkJBQ0MsOERBQUMxQixzSEFBU0E7b0NBQUNJLFdBQVU7Ozs7O3lEQUVyQiw4REFBQ0osc0hBQVNBO29DQUFDSSxXQUFVOzs7Ozs7Z0NBQ3JCOzs7Ozs7Ozs7Ozs7O2dCQUtMd0IsMkJBQ0MsOERBQUNtQjtvQkFBRTNDLFdBQVU7O3dCQUFxQzt3QkFDakN3QixVQUFVTCxrQkFBa0I7Ozs7Ozs7Z0JBSTlDLENBQUNsQiwwQkFDQSw4REFBQ2U7b0JBQUloQixXQUFVOzhCQUNiLDRFQUFDZ0I7d0JBQUloQixXQUFVOzswQ0FDYiw4REFBQ0gsc0hBQWFBO2dDQUFDRyxXQUFVOzs7Ozs7MENBQ3pCLDhEQUFDZ0I7Z0NBQUloQixXQUFVOztrREFDYiw4REFBQzJDO3dDQUFFM0MsV0FBVTtrREFBbUM7Ozs7OztrREFDaEQsOERBQUMyQzt3Q0FBRTNDLFdBQVU7a0RBQWtCOzs7Ozs7a0RBRy9CLDhEQUFDMkM7d0NBQUUzQyxXQUFVOzs0Q0FBK0I7MERBQ3BCLDhEQUFDNEM7Z0RBQUs1QyxXQUFVOzBEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBTzVFQywwQkFDQyw4REFBQ2U7b0JBQUloQixXQUFVOzhCQUNiLDRFQUFDZ0I7d0JBQUloQixXQUFVOzswQ0FDYiw4REFBQ04sc0hBQUlBO2dDQUFDTSxXQUFVOzs7Ozs7MENBQ2hCLDhEQUFDMkM7Z0NBQUUzQyxXQUFVOzBDQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNwRDtJQWxIZ0JvQjtNQUFBQSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdWlcXGJhY2tlbmQtc3RhdHVzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgV2lmaSwgV2lmaU9mZiwgUmVmcmVzaEN3LCBBbGVydFRyaWFuZ2xlLCBDbG9jayB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmludGVyZmFjZSBCYWNrZW5kU3RhdHVzUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNob3dEZXRhaWxzPzogYm9vbGVhbjtcbn1cblxuaW50ZXJmYWNlIE5ldHdvcmtTdGF0dXNQcm9wcyB7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE5ldHdvcmtTdGF0dXMoeyBjbGFzc05hbWUgPSBcIlwiIH06IE5ldHdvcmtTdGF0dXNQcm9wcykge1xuICBjb25zdCBbaXNPbmxpbmUsIHNldElzT25saW5lXSA9IHVzZVN0YXRlKG5hdmlnYXRvci5vbkxpbmUpO1xuICBjb25zdCBbY3VycmVudFRpbWUsIHNldEN1cnJlbnRUaW1lXSA9IHVzZVN0YXRlKG5ldyBEYXRlKCkpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlT25saW5lID0gKCkgPT4gc2V0SXNPbmxpbmUodHJ1ZSk7XG4gICAgY29uc3QgaGFuZGxlT2ZmbGluZSA9ICgpID0+IHNldElzT25saW5lKGZhbHNlKTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvbmxpbmUnLCBoYW5kbGVPbmxpbmUpO1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvZmZsaW5lJywgaGFuZGxlT2ZmbGluZSk7XG5cbiAgICAvLyBVcGRhdGUgdGltZSBldmVyeSBzZWNvbmRcbiAgICBjb25zdCB0aW1lSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50VGltZShuZXcgRGF0ZSgpKTtcbiAgICB9LCAxMDAwKTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignb25saW5lJywgaGFuZGxlT25saW5lKTtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdvZmZsaW5lJywgaGFuZGxlT2ZmbGluZSk7XG4gICAgICBjbGVhckludGVydmFsKHRpbWVJbnRlcnZhbCk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxCYWRnZVxuICAgICAgICB2YXJpYW50PXtpc09ubGluZSA/IFwiZGVmYXVsdFwiIDogXCJkZXN0cnVjdGl2ZVwifVxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiXG4gICAgICA+XG4gICAgICAgIHtpc09ubGluZSA/IDxXaWZpIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPiA6IDxXaWZpT2ZmIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPn1cbiAgICAgICAge2lzT25saW5lID8gXCJPbmxpbmVcIiA6IFwiT2ZmbGluZVwifVxuICAgICAgPC9CYWRnZT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICA8c3Bhbj57Y3VycmVudFRpbWUudG9Mb2NhbGVUaW1lU3RyaW5nKCl9PC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBCYWNrZW5kU3RhdHVzKHsgY2xhc3NOYW1lID0gXCJcIiwgc2hvd0RldGFpbHMgPSBmYWxzZSB9OiBCYWNrZW5kU3RhdHVzUHJvcHMpIHtcbiAgY29uc3QgW2lzT25saW5lLCBzZXRJc09ubGluZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0NoZWNraW5nLCBzZXRJc0NoZWNraW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xhc3RDaGVjaywgc2V0TGFzdENoZWNrXSA9IHVzZVN0YXRlPERhdGUgfCBudWxsPihudWxsKTtcblxuICBjb25zdCBjaGVja0JhY2tlbmRTdGF0dXMgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNDaGVja2luZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4gY29udHJvbGxlci5hYm9ydCgpLCAyMDAwKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cDovL2xvY2FsaG9zdDo1MDAwLycsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbFxuICAgICAgfSk7XG5cbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgc2V0SXNPbmxpbmUocmVzcG9uc2Uuc3RhdHVzIDwgNTAwKTtcbiAgICAgIHNldExhc3RDaGVjayhuZXcgRGF0ZSgpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0SXNPbmxpbmUoZmFsc2UpO1xuICAgICAgc2V0TGFzdENoZWNrKG5ldyBEYXRlKCkpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjaGVja0JhY2tlbmRTdGF0dXMoKTtcblxuICAgIC8vIENoZWNrIGV2ZXJ5IDMwIHNlY29uZHNcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGNoZWNrQmFja2VuZFN0YXR1cywgMzAwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbXSk7XG5cbiAgaWYgKCFzaG93RGV0YWlscykge1xuICAgIHJldHVybiAoXG4gICAgICA8QmFkZ2VcbiAgICAgICAgdmFyaWFudD17aXNPbmxpbmUgPyBcImRlZmF1bHRcIiA6IFwiZGVzdHJ1Y3RpdmVcIn1cbiAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgJHtjbGFzc05hbWV9YH1cbiAgICAgID5cbiAgICAgICAge2lzT25saW5lID8gPFdpZmkgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+IDogPFdpZmlPZmYgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+fVxuICAgICAgICB7aXNPbmxpbmUgPyBcIkJhY2tlbmQgT25saW5lXCIgOiBcIkJhY2tlbmQgT2ZmbGluZVwifVxuICAgICAgPC9CYWRnZT5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZCBjbGFzc05hbWU9e2Ake2NsYXNzTmFtZX1gfT5cbiAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICB7aXNPbmxpbmUgPyAoXG4gICAgICAgICAgICAgIDxXaWZpIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPFdpZmlPZmYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgIEJhY2tlbmQgU3RhdHVzOiB7aXNPbmxpbmUgPyBcIk9ubGluZVwiIDogXCJPZmZsaW5lXCJ9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrQmFja2VuZFN0YXR1c31cbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0NoZWNraW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc0NoZWNraW5nID8gKFxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtMyB3LTMgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgQ2hlY2tcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7bGFzdENoZWNrICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0yXCI+XG4gICAgICAgICAgICBMYXN0IGNoZWNrZWQ6IHtsYXN0Q2hlY2sudG9Mb2NhbGVUaW1lU3RyaW5nKCl9XG4gICAgICAgICAgPC9wPlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAgeyFpc09ubGluZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIHAtMyBiZy15ZWxsb3ctNTAwLzEwIGJvcmRlciBib3JkZXIteWVsbG93LTUwMC8yMCByb3VuZGVkLW1kXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXllbGxvdy02MDAgbXQtMC41IGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXllbGxvdy02MDAgbWItMVwiPkJhY2tlbmQgT2ZmbGluZTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy03MDBcIj5cbiAgICAgICAgICAgICAgICAgIFNvbWUgZmVhdHVyZXMgbWF5IGJlIGxpbWl0ZWQuIFNlc3Npb24gbWFuYWdlbWVudCB3aWxsIHVzZSBsb2NhbCBzdG9yYWdlLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQteWVsbG93LTYwMCBtdC0yXCI+XG4gICAgICAgICAgICAgICAgICBUbyBzdGFydCB0aGUgYmFja2VuZDogPGNvZGUgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCBweC0xIHJvdW5kZWRcIj5weXRob24gcnVuLnB5PC9jb2RlPlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHtpc09ubGluZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zIHAtMyBiZy1ncmVlbi01MDAvMTAgYm9yZGVyIGJvcmRlci1ncmVlbi01MDAvMjAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8V2lmaSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTcwMFwiPlxuICAgICAgICAgICAgICAgIEFsbCBmZWF0dXJlcyBhdmFpbGFibGUuIEJhY2tlbmQgY29ubmVjdGVkIHN1Y2Nlc3NmdWxseS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQmFkZ2UiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJXaWZpIiwiV2lmaU9mZiIsIlJlZnJlc2hDdyIsIkFsZXJ0VHJpYW5nbGUiLCJDbG9jayIsIk5ldHdvcmtTdGF0dXMiLCJjbGFzc05hbWUiLCJpc09ubGluZSIsInNldElzT25saW5lIiwibmF2aWdhdG9yIiwib25MaW5lIiwiY3VycmVudFRpbWUiLCJzZXRDdXJyZW50VGltZSIsIkRhdGUiLCJoYW5kbGVPbmxpbmUiLCJoYW5kbGVPZmZsaW5lIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInRpbWVJbnRlcnZhbCIsInNldEludGVydmFsIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNsZWFySW50ZXJ2YWwiLCJkaXYiLCJ2YXJpYW50Iiwic3BhbiIsInRvTG9jYWxlVGltZVN0cmluZyIsIkJhY2tlbmRTdGF0dXMiLCJzaG93RGV0YWlscyIsImlzQ2hlY2tpbmciLCJzZXRJc0NoZWNraW5nIiwibGFzdENoZWNrIiwic2V0TGFzdENoZWNrIiwiY2hlY2tCYWNrZW5kU3RhdHVzIiwiY29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJhYm9ydCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJzaWduYWwiLCJjbGVhclRpbWVvdXQiLCJzdGF0dXMiLCJlcnJvciIsImludGVydmFsIiwic2l6ZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInAiLCJjb2RlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/backend-status.tsx\n"));

/***/ })

});